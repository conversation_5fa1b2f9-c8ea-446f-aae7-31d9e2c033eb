import 'package:flutter/material.dart';
import '../../constants/user_roles.dart';

class ContextSelector extends StatelessWidget {
  final String selectedContext;
  final ValueChanged<String> onContextChanged;
  final UserRole userRole;

  const ContextSelector({
    super.key,
    required this.selectedContext,
    required this.onContextChanged,
    required this.userRole,
  });

  @override
  Widget build(BuildContext context) {
    final availableContexts = _getAvailableContexts();
    
    return Container(
      height: 56,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: availableContexts.length,
        // Performance optimizations for horizontal list
        cacheExtent: 500,
        addRepaintBoundaries: true,
        itemBuilder: (context, index) {
          final contextItem = availableContexts[index];
          final isSelected = contextItem.name == selectedContext;
          final isLocked = contextItem.isPremium && !userRole.isPremium;
          
          return Container(
            margin: const EdgeInsets.only(right: 8),
            child: FilterChip(
              label: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    contextItem.icon,
                    size: 16,
                    color: isSelected 
                        ? Colors.white 
                        : isLocked 
                            ? Colors.grey[400]
                            : Theme.of(context).primaryColor,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    contextItem.name,
                    style: TextStyle(
                      color: isSelected 
                          ? Colors.white 
                          : isLocked 
                              ? Colors.grey[400]
                              : Colors.black87,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                  if (isLocked) ...[
                    const SizedBox(width: 4),
                    Icon(
                      Icons.lock,
                      size: 12,
                      color: Colors.grey[400],
                    ),
                  ],
                ],
              ),
              selected: isSelected,
              onSelected: isLocked 
                  ? null 
                  : (selected) {
                      if (selected) {
                        onContextChanged(contextItem.name);
                      }
                    },
              selectedColor: Theme.of(context).primaryColor,
              backgroundColor: isLocked ? Colors.grey[100] : Colors.white,
              side: BorderSide(
                color: isSelected 
                    ? Theme.of(context).primaryColor
                    : isLocked 
                        ? Colors.grey[300]!
                        : Colors.grey[400]!,
              ),
            ),
          );
        },
      ),
    );
  }

  List<ContextItem> _getAvailableContexts() {
    return [
      ContextItem(
        name: 'Trợ lý học tập',
        icon: Icons.school,
        isPremium: false,
      ),
      ContextItem(
        name: 'Lập trình viên',
        icon: Icons.code,
        isPremium: false,
      ),
      ContextItem(
        name: 'Dịch thuật',
        icon: Icons.translate,
        isPremium: false,
      ),
      ContextItem(
        name: 'Tư vấn sức khỏe',
        icon: Icons.health_and_safety,
        isPremium: true,
      ),
      ContextItem(
        name: 'Sáng tạo nội dung',
        icon: Icons.create,
        isPremium: true,
      ),
      ContextItem(
        name: 'Tư vấn tài chính',
        icon: Icons.account_balance,
        isPremium: true,
      ),
      ContextItem(
        name: 'Luật sư',
        icon: Icons.gavel,
        isPremium: true,
      ),
      ContextItem(
        name: 'Bác sĩ tâm lý',
        icon: Icons.psychology,
        isPremium: true,
      ),
      ContextItem(
        name: 'Chuyên gia marketing',
        icon: Icons.campaign,
        isPremium: true,
      ),
      ContextItem(
        name: 'Đầu bếp',
        icon: Icons.restaurant,
        isPremium: true,
      ),
      ContextItem(
        name: 'Huấn luyện viên',
        icon: Icons.fitness_center,
        isPremium: true,
      ),
      ContextItem(
        name: 'Nhà thiết kế',
        icon: Icons.design_services,
        isPremium: true,
      ),
    ];
  }
}

class ContextItem {
  final String name;
  final IconData icon;
  final bool isPremium;

  ContextItem({
    required this.name,
    required this.icon,
    required this.isPremium,
  });
}
