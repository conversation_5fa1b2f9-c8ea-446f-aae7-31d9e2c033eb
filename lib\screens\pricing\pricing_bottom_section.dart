import 'package:flutter/material.dart';
import '../../models/pricing_ui_models.dart';

class PricingBottomSection extends StatelessWidget {
  final PricingPlan selectedPlan;
  final VoidCallback onContinue;
  final bool isLoading;

  const PricingBottomSection({
    super.key,
    required this.selectedPlan,
    required this.onContinue,
    required this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 10,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: Safe<PERSON>rea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildSelectedPlanInfo(),
            const SizedBox(height: 16),
            _buildContinueButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectedPlanInfo() {
    return Row(
      children: [
        Icon(
          Icons.check_circle,
          color: selectedPlan.color,
          size: 24,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Gói đã chọn: ${selectedPlan.name}',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: selectedPlan.color,
                ),
              ),
              Text(
                selectedPlan.price == '0'
                    ? 'Miễn phí mãi mãi'
                    : '${selectedPlan.price} VNĐ/${selectedPlan.period}',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        ),
        Text(
          selectedPlan.tokenLimitFormatted,
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: selectedPlan.color,
          ),
        ),
      ],
    );
  }

  Widget _buildContinueButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: isLoading ? null : onContinue,
        style: ElevatedButton.styleFrom(
          backgroundColor: selectedPlan.isFree ? Colors.grey[400] : Colors.blue,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 0,
        ),
        child: isLoading
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(
                selectedPlan.isFree ? 'Gói hiện tại' : 'Tiếp tục',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }
}
