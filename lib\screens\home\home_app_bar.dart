import 'package:flutter/material.dart';
import '../chat_screen.dart';
import '../admin/admin_dashboard_screen.dart';
import '../admin/user_management_screen.dart';
import '../../constants/user_roles.dart';

class HomeAppBar extends StatelessWidget implements PreferredSizeWidget {
  final UserRole userRole;
  final String userName;
  final VoidCallback onLogout;

  const HomeAppBar({
    super.key,
    required this.userRole,
    required this.userName,
    required this.onLogout,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text('AI Assistant'),
          if (userRole.isPremium) ...[
            const SizedBox(width: 8),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.orange[600],
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Text(
                'PRO',
                style: TextStyle(
                  fontSize: 9,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ],
      ),
      backgroundColor: Theme.of(context).primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      automaticallyImplyLeading: false,
      actions: [
        // Chat button
        IconButton(
          icon: const Icon(Icons.chat_bubble_outline),
          tooltip: 'Chat',
          onPressed: () => _navigateToChat(context),
        ),

        // Admin menu
        if (userRole.isAdmin) _buildAdminMenu(context),

        // More menu
        _buildMoreMenu(context),
      ],
    );
  }

  Widget _buildAdminMenu(BuildContext context) {
    return PopupMenuButton<String>(
      icon: const Icon(Icons.admin_panel_settings),
      tooltip: 'Admin Menu',
      onSelected: (value) => _handleAdminMenuSelection(context, value),
      itemBuilder: (context) => const [
        PopupMenuItem(
          value: 'dashboard',
          child: Row(
            children: [
              Icon(Icons.dashboard, size: 20),
              SizedBox(width: 12),
              Text('Admin Dashboard'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'users',
          child: Row(
            children: [
              Icon(Icons.people, size: 20),
              SizedBox(width: 12),
              Text('User Management'),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMoreMenu(BuildContext context) {
    return PopupMenuButton<String>(
      icon: const Icon(Icons.more_vert),
      tooltip: 'More',
      onSelected: (value) => _handleMoreMenuSelection(context, value),
      itemBuilder: (context) => const [
        PopupMenuItem(
          value: 'notifications',
          child: Row(
            children: [
              Icon(Icons.notifications_outlined, size: 20),
              SizedBox(width: 12),
              Text('Thông báo'),
            ],
          ),
        ),
        PopupMenuItem(
          value: 'logout',
          child: Row(
            children: [
              Icon(Icons.logout, size: 20, color: Colors.red),
              SizedBox(width: 12),
              Text('Đăng xuất', style: TextStyle(color: Colors.red)),
            ],
          ),
        ),
      ],
    );
  }

  void _navigateToChat(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatScreen(
          userName: userName,
          isPremium: userRole.isPremium,
          userRole: userRole,
        ),
      ),
    );
  }

  void _handleAdminMenuSelection(BuildContext context, String value) {
    switch (value) {
      case 'dashboard':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => AdminDashboardScreen(adminName: userName),
          ),
        );
        break;
      case 'users':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const UserManagementScreen(),
          ),
        );
        break;
    }
  }

  void _handleMoreMenuSelection(BuildContext context, String value) {
    switch (value) {
      case 'notifications':
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Thông báo')),
        );
        break;
      case 'logout':
        onLogout();
        break;
    }
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
