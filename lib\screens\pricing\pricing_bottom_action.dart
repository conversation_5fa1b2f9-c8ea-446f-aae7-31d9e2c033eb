import 'package:flutter/material.dart';
import '../../models/pricing_ui_models.dart';

class PricingBottomAction extends StatelessWidget {
  final PricingPlan selectedPlan;
  final VoidCallback onContinue;
  final bool isLoading;

  const PricingBottomAction({
    super.key,
    required this.selectedPlan,
    required this.onContinue,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final isSmallScreen = screenHeight < 700;

    return Container(
      padding: EdgeInsets.all(isSmallScreen ? 12 : 16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Selected plan summary - compact on small screens
            if (!isSmallScreen) ...[
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: selectedPlan.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: selectedPlan.color.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.check_circle,
                      color: selectedPlan.color,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Gói đã chọn: ${selectedPlan.name}',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: selectedPlan.color,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            selectedPlan.price == '0'
                                ? 'Miễn phí mãi mãi'
                                : '${selectedPlan.price} VNĐ/${selectedPlan.period}',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    Text(
                      selectedPlan.tokenLimitFormatted,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: selectedPlan.color,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),
            ] else ...[
              // Compact summary for small screens
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  color: selectedPlan.color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '${selectedPlan.name} - ${selectedPlan.price == '0' ? 'Miễn phí' : '${selectedPlan.price}₫'}',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: selectedPlan.color,
                      ),
                    ),
                    Text(
                      selectedPlan.tokenLimitFormatted,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: selectedPlan.color,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 12),
            ],

            // Continue button
            SizedBox(
              width: double.infinity,
              height: isSmallScreen ? 45 : 50,
              child: ElevatedButton(
                onPressed: selectedPlan.isCurrentPlan || isLoading
                    ? null
                    : onContinue,
                style: ElevatedButton.styleFrom(
                  backgroundColor: selectedPlan.color,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child: isLoading
                    ? SizedBox(
                        height: isSmallScreen ? 18 : 20,
                        width: isSmallScreen ? 18 : 20,
                        child: const CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        _getButtonText(),
                        style: TextStyle(
                          fontSize: isSmallScreen ? 14 : 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
              ),
            ),

            SizedBox(height: isSmallScreen ? 6 : 8),

            // Terms text - smaller on small screens
            Text(
              'Bằng cách tiếp tục, bạn đồng ý với Điều khoản sử dụng và Chính sách bảo mật',
              style: TextStyle(
                fontSize: isSmallScreen ? 10 : 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
              maxLines: isSmallScreen ? 2 : null,
            ),
          ],
        ),
      ),
    );
  }

  String _getButtonText() {
    if (selectedPlan.isCurrentPlan) {
      return 'Gói hiện tại';
    } else if (selectedPlan.isFree) {
      return 'Sử dụng gói miễn phí';
    } else {
      return 'Tiếp tục thanh toán';
    }
  }
}
