import 'package:flutter/material.dart';
import 'pricing_screen.dart';
import '../constants/user_roles.dart';
import '../models/chat_models.dart' as chat_models;
import '../services/chat_service.dart';
import 'chat/chat_app_bar.dart';
import 'chat/chat_message_bubble.dart';
import 'chat/chat_input.dart';
import 'chat/context_selector.dart';
import 'chat/typing_indicator.dart';

class ChatScreen extends StatefulWidget {
  final String userName;
  final bool isPremium;
  final UserRole? userRole;

  const ChatScreen({
    super.key,
    required this.userName,
    this.isPremium = false,
    this.userRole,
  });

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final List<chat_models.ChatMessage> _messages = [];

  UserRole get _userRole => widget.userRole ?? UserRole.user;

  int get _messageLimit => PermissionHelper.getMessageLimit(_userRole);
  int _messageCount = 0;

  bool get _canChatUnlimited => PermissionHelper.canChatUnlimited(_userRole);

  String _selectedContext = 'Trợ lý học tập';
  bool _isTyping = false;
  final bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _addWelcomeMessage();
    _loadRemainingTokens();
  }

  @override
  void dispose() {
    // Proper cleanup to prevent memory leaks
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _addWelcomeMessage() {
    setState(() {
      _messages.add(
        chat_models.ChatMessage(
          message:
              'Xin chào ${widget.userName}! Tôi là AI Assistant. ${_getRoleWelcomeMessage()}',
          isUser: false,
          timestamp: DateTime.now(),
        ),
      );
    });
  }

  String _getRoleWelcomeMessage() {
    if (_canChatUnlimited) {
      return 'Bạn có thể chat không giới hạn. Tôi có thể hỗ trợ gì cho bạn hôm nay?';
    } else {
      return 'Bạn có $_messageLimit tin nhắn miễn phí hôm nay. Bạn muốn tôi hỗ trợ gì?';
    }
  }

  void _loadRemainingTokens() async {
    try {
      final tokenResponse = await ChatService.getRemainingTokens();
      if (tokenResponse.success && tokenResponse.data != null) {
        // Token information loaded successfully
        // Could be used for display in UI if needed
      }
    } catch (e) {
      // Ignore error for now
    }
  }

  void _updateRemainingTokens() async {
    _loadRemainingTokens();
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Vừa xong';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes} phút trước';
    } else if (difference.inDays < 1) {
      return '${difference.inHours} giờ trước';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year} ${timestamp.hour}:${timestamp.minute.toString().padLeft(2, '0')}';
    }
  }

  void _clearChat() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Xóa lịch sử chat'),
          content: const Text(
            'Bạn có chắc chắn muốn xóa toàn bộ lịch sử chat? '
            'Hành động này không thể hoàn tác.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Hủy'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                setState(() {
                  _messages.clear();
                  _addWelcomeMessage();
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Xóa'),
            ),
          ],
        );
      },
    );
  }

  void _exportChat() {
    if (_messages.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Không có tin nhắn để xuất'),
        ),
      );
      return;
    }

    // Create export content
    final StringBuffer buffer = StringBuffer();
    buffer.writeln('=== Lịch sử Chat AI Assistant ===');
    buffer.writeln('Ngày xuất: ${DateTime.now().toString().split('.')[0]}');
    buffer.writeln('Context: $_selectedContext');
    buffer.writeln('Tổng số tin nhắn: ${_messages.length}');
    buffer.writeln('');

    for (int i = 0; i < _messages.length; i++) {
      final message = _messages[i];
      final sender = message.isUser ? 'Bạn' : 'AI Assistant';
      final time = _formatTimestamp(message.timestamp);

      buffer.writeln('[$time] $sender:');
      buffer.writeln(message.message);
      if (!message.isUser && message.tokensUsed != null) {
        buffer.writeln('(Sử dụng ${message.tokensUsed} tokens)');
      }
      buffer.writeln('');
    }

    // For now, just show success message
    // In a real app, you would save to file or share
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Đã xuất ${_messages.length} tin nhắn'),
        action: SnackBarAction(
          label: 'Xem',
          onPressed: () {
            showDialog(
              context: context,
              builder: (context) => AlertDialog(
                title: const Text('Nội dung xuất'),
                content: SingleChildScrollView(
                  child: Text(buffer.toString()),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Đóng'),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  void _sendMessage() async {
    if (_messageController.text.trim().isEmpty) return;

    // Validate message
    if (!ChatService.isValidMessage(_messageController.text)) {
      _showErrorDialog(
        'Tin nhắn không hợp lệ. Vui lòng nhập tin nhắn từ 1-1000 ký tự.',
      );
      return;
    }

    if (!_canChatUnlimited && _messageCount >= _messageLimit) {
      _showUpgradeDialog();
      return;
    }

    final userMessage = _messageController.text.trim();
    _messageController.clear();

    setState(() {
      _messages.add(
        chat_models.ChatMessage.fromUserMessage(userMessage),
      );
      _isTyping = true;
      _messageCount++;
    });

    _scrollToBottom();

    try {
      // Gọi ChatService để gửi tin nhắn
      final chatResponse = await ChatService.sendMessage(
        message: userMessage,
        context: _selectedContext,
        userRole: _userRole,
        model: 'gpt-4o',
      );

      setState(() {
        _isTyping = false;

        if (chatResponse.success && chatResponse.data != null) {
          _messages.add(
            chat_models.ChatMessage(
              message: chatResponse.data!.response,
              isUser: false,
              timestamp: DateTime.now(),
            ),
          );

          // Cập nhật remaining tokens
          _updateRemainingTokens();
        } else {
          // Kiểm tra nếu là lỗi authentication
          if (chatResponse.message.contains('đăng nhập') ||
              chatResponse.message.contains('hết hạn')) {
            _handleAuthenticationError(chatResponse.message);
            return;
          }

          // Hiển thị error message nếu API thất bại
          _messages.add(
            chat_models.ChatMessage(
              message: chatResponse.message.isNotEmpty
                  ? chatResponse.message
                  : 'Xin lỗi, tôi không thể trả lời câu hỏi này. Vui lòng thử lại sau.',
              isUser: false,
              timestamp: DateTime.now(),
            ),
          );
        }
      });
    } catch (e) {
      setState(() {
        _isTyping = false;
        _messages.add(
          chat_models.ChatMessage(
            message: 'Đã xảy ra lỗi kết nối. Vui lòng kiểm tra mạng và thử lại.',
            isUser: false,
            timestamp: DateTime.now(),
          ),
        );
      });
    }

    _scrollToBottom();
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _showErrorDialog(String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Lỗi'),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Đóng'),
            ),
          ],
        );
      },
    );
  }

  void _showUpgradeDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Giới hạn tin nhắn'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.lock, size: 64, color: Colors.orange[600]),
              const SizedBox(height: 16),
              Text(
                'Bạn đã sử dụng hết $_messageLimit tin nhắn miễn phí.',
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              const Text(
                'Nâng cấp lên Premium để chat không giới hạn!',
                textAlign: TextAlign.center,
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Để sau'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const PricingScreen(),
                  ),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange[600],
                foregroundColor: Colors.white,
              ),
              child: const Text('Nâng cấp'),
            ),
          ],
        );
      },
    );
  }

  void _handleAuthenticationError(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Phiên đăng nhập hết hạn'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.lock_outline, size: 64, color: Colors.red[600]),
              const SizedBox(height: 16),
              Text(message, textAlign: TextAlign.center),
              const SizedBox(height: 8),
              const Text(
                'Vui lòng đăng nhập lại để tiếp tục sử dụng.',
                textAlign: TextAlign.center,
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          actions: [
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context); // Đóng dialog
                Navigator.pop(context); // Quay lại màn hình trước (login/home)
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red[600],
                foregroundColor: Colors.white,
              ),
              child: const Text('Đăng nhập lại'),
            ),
          ],
        );
      },
    );
  }







  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ChatAppBar(
        userName: widget.userName,
        userRole: _userRole,
        selectedContext: _selectedContext,
        onClearChat: _clearChat,
        onExportChat: _userRole.isPremium ? _exportChat : null,
      ),
      body: Column(
        children: [
          // Context Selector
          ContextSelector(
            selectedContext: _selectedContext,
            onContextChanged: (context) {
              setState(() {
                _selectedContext = context;
              });
            },
            userRole: _userRole,
          ),

          // Messages List - Optimized with caching
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              itemCount: _messages.length + (_isTyping ? 1 : 0),
              // Performance optimizations
              cacheExtent: 1000, // Cache items outside viewport
              addAutomaticKeepAlives: true, // Keep items alive when scrolled away
              addRepaintBoundaries: true, // Isolate repaints
              itemBuilder: (context, index) {
                if (index == _messages.length && _isTyping) {
                  return const TypingIndicator(isVisible: true);
                }
                // Use RepaintBoundary for each message to isolate repaints
                return RepaintBoundary(
                  child: ChatMessageBubble(
                    key: ValueKey(_messages[index].timestamp.millisecondsSinceEpoch),
                    message: _messages[index],
                  ),
                );
              },
            ),
          ),

          // Chat Input
          ChatInput(
            controller: _messageController,
            onSend: _sendMessage,
            isEnabled: !_isLoading,
            isLoading: _isTyping,
            hintText: 'Nhập tin nhắn...',
            remainingMessages: _canChatUnlimited ? null : (_messageLimit - _messageCount),
            canChatUnlimited: _canChatUnlimited,
          ),
        ],
      ),
    );
  }
}
