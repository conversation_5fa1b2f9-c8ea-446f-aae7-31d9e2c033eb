import 'package:flutter/material.dart';
import '../../models/pricing_ui_models.dart';

class PricingCard extends StatelessWidget {
  final PricingPlan plan;
  final bool isSelected;
  final VoidCallback onTap;

  const PricingCard({
    super.key,
    required this.plan,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final isSmallScreen = screenHeight < 700;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: EdgeInsets.symmetric(
          horizontal: 16,
          vertical: isSmallScreen ? 6 : 8,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected
                ? plan.color
                : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.08),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
            if (isSelected)
              BoxShadow(
                color: plan.color.withValues(alpha: 0.2),
                blurRadius: 16,
                offset: const Offset(0, 8),
              ),
          ],
        ),
        child: Column(
          children: [
            // Header with popular badge
            if (plan.isPopular) _buildPopularBadge(),

            // Plan header
            Container(
              padding: EdgeInsets.all(isSmallScreen ? 16 : 20),
              child: Column(
                children: [
                  // Plan name and current indicator
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Flexible(
                        child: Text(
                          plan.name,
                          style: TextStyle(
                            fontSize: isSmallScreen ? 20 : 24,
                            fontWeight: FontWeight.bold,
                            color: plan.color,
                          ),
                        ),
                      ),
                      if (plan.isCurrentPlan)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.green[100],
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            'Hiện tại',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.green[700],
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                    ],
                  ),
                  SizedBox(height: isSmallScreen ? 6 : 8),

                  // Price
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Flexible(
                        child: Text(
                          plan.price == '0' ? 'Miễn phí' : '${plan.price} VNĐ',
                          style: TextStyle(
                            fontSize: isSmallScreen ? 26 : 32,
                            fontWeight: FontWeight.bold,
                            color: plan.color,
                          ),
                        ),
                      ),
                      if (plan.price != '0') ...[
                        const SizedBox(width: 4),
                        Text(
                          '/${plan.period}',
                          style: TextStyle(
                            fontSize: isSmallScreen ? 14 : 16,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ],
                  ),
                  SizedBox(height: isSmallScreen ? 6 : 8),

                  // Token limit
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: isSmallScreen ? 10 : 12,
                      vertical: isSmallScreen ? 4 : 6,
                    ),
                    decoration: BoxDecoration(
                      color: plan.color.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      plan.tokenLimitFormatted,
                      style: TextStyle(
                        fontSize: isSmallScreen ? 12 : 14,
                        color: plan.color,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Features - compact on small screens
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: isSmallScreen ? 16 : 20,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Tính năng:',
                    style: TextStyle(
                      fontSize: isSmallScreen ? 14 : 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[800],
                    ),
                  ),
                  SizedBox(height: isSmallScreen ? 6 : 8),

                  // Show limited features on small screens
                  if (isSmallScreen) ...[
                    ...plan.features.take(3).map((feature) => _buildFeatureItem(
                      feature,
                      true,
                      plan.color,
                      isSmallScreen,
                    )),
                    if (plan.features.length > 3)
                      _buildFeatureItem(
                        '+${plan.features.length - 3} tính năng khác',
                        true,
                        Colors.grey[600]!,
                        isSmallScreen,
                      ),
                  ] else ...[
                    ...plan.features.map((feature) => _buildFeatureItem(
                      feature,
                      true,
                      plan.color,
                      isSmallScreen,
                    )),
                  ],

                  // Limitations - only show on larger screens or if critical
                  if (plan.limitations.isNotEmpty && (!isSmallScreen || plan.limitations.length <= 2)) ...[
                    SizedBox(height: isSmallScreen ? 8 : 12),
                    Text(
                      'Giới hạn:',
                      style: TextStyle(
                        fontSize: isSmallScreen ? 14 : 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey[800],
                      ),
                    ),
                    SizedBox(height: isSmallScreen ? 4 : 8),
                    ...plan.limitations.take(isSmallScreen ? 2 : plan.limitations.length).map((limitation) => _buildFeatureItem(
                      limitation,
                      false,
                      Colors.orange[600]!,
                      isSmallScreen,
                    )),
                  ],
                ],
              ),
            ),

            SizedBox(height: isSmallScreen ? 12 : 20),
          ],
        ),
      ),
    );
  }

  Widget _buildPopularBadge() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        color: Colors.orange[600],
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: const Text(
        '🔥 PHỔ BIẾN NHẤT',
        textAlign: TextAlign.center,
        style: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.bold,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String text, bool isIncluded, Color color, [bool isSmallScreen = false]) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: isSmallScreen ? 2 : 4),
      child: Row(
        children: [
          Icon(
            isIncluded ? Icons.check_circle : Icons.cancel,
            size: isSmallScreen ? 16 : 20,
            color: color,
          ),
          SizedBox(width: isSmallScreen ? 6 : 8),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: isSmallScreen ? 12 : 14,
                color: Colors.grey[700],
              ),
              maxLines: isSmallScreen ? 1 : null,
              overflow: isSmallScreen ? TextOverflow.ellipsis : null,
            ),
          ),
        ],
      ),
    );
  }
}
