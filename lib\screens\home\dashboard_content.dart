import 'package:flutter/material.dart';
import '../../constants/user_roles.dart';
import 'welcome_card.dart';
import 'role_card.dart';
import 'usage_stats_card.dart';
import 'admin_quick_access.dart';
import 'ai_contexts_grid.dart';
import 'recent_activities.dart';
import 'quick_actions.dart';

class DashboardContent extends StatelessWidget {
  final String userName;
  final String userEmail;
  final UserRole userRole;
  final int todayMessages;
  final int totalMessages;

  const DashboardContent({
    super.key,
    required this.userName,
    required this.userEmail,
    required this.userRole,
    required this.todayMessages,
    required this.totalMessages,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome Card
          WelcomeCard(
            userName: userName,
            userEmail: userEmail,
          ),
          const SizedBox(height: 16),

          // Role Card
          RoleCard(userRole: userRole),
          const SizedBox(height: 16),

          // Admin Quick Access (only for admins)
          if (userRole.isAdmin) ...[
            AdminQuickAccess(adminName: userName),
            const SizedBox(height: 16),
          ],

          // Usage Stats
          UsageStatsCard(
            userRole: userRole,
            todayMessages: todayMessages,
            totalMessages: totalMessages,
          ),
          const SizedBox(height: 24),

          // Quick Actions
          QuickActions(
            userName: userName,
            userRole: userRole,
          ),
          const SizedBox(height: 24),

          // AI Contexts Grid
          AIContextsGrid(
            userName: userName,
            userRole: userRole,
          ),
          const SizedBox(height: 24),

          // Recent Activities
          const RecentActivities(),
        ],
      ),
    );
  }
}
