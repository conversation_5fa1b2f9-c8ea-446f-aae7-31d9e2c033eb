import 'package:flutter/material.dart';

// Model cho Admin User từ API
class AdminUser {
  final int id;
  final String username;
  final String role;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final bool hasPassword;

  AdminUser({
    required this.id,
    required this.username,
    required this.role,
    required this.createdAt,
    this.updatedAt,
    required this.hasPassword,
  });

  factory AdminUser.fromJson(Map<String, dynamic> json) {
    return AdminUser(
      id: json['id'] as int,
      username: json['username'] as String,
      role: json['role'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'] as String)
          : null,
      hasPassword: json['hasPassword'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'role': role,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'hasPassword': hasPassword,
    };
  }

  // Helper methods
  String get normalizedRole => role.toLowerCase();

  Color get roleColor {
    switch (normalizedRole) {
      case 'admin':
        return Colors.red;
      case 'premium':
        return Colors.orange;
      case 'user':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  IconData get roleIcon {
    switch (normalizedRole) {
      case 'admin':
        return Icons.admin_panel_settings;
      case 'premium':
        return Icons.star;
      case 'user':
        return Icons.person;
      default:
        return Icons.help;
    }
  }

  String get formattedCreatedAt {
    return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
  }

  String get statusText {
    return hasPassword ? 'Hoạt động' : 'Chưa kích hoạt';
  }
}

// Response wrapper cho Users API
class UsersResponse {
  final int count;
  final List<AdminUser> users;

  UsersResponse({required this.count, required this.users});

  factory UsersResponse.fromJson(Map<String, dynamic> json) {
    return UsersResponse(
      count: json['count'] as int,
      users: (json['users'] as List<dynamic>)
          .map(
            (userJson) => AdminUser.fromJson(userJson as Map<String, dynamic>),
          )
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'count': count,
      'users': users.map((user) => user.toJson()).toList(),
    };
  }

  // Helper methods
  List<AdminUser> get adminUsers =>
      users.where((u) => u.normalizedRole == 'admin').toList();
  List<AdminUser> get premiumUsers =>
      users.where((u) => u.normalizedRole == 'premium').toList();
  List<AdminUser> get regularUsers =>
      users.where((u) => u.normalizedRole == 'user').toList();

  int get adminCount => adminUsers.length;
  int get premiumCount => premiumUsers.length;
  int get regularUserCount => regularUsers.length;
}

// Model cho Database Tables info
class DatabaseTables {
  final int users;
  final int chatHistories;
  final int subscriptions;

  DatabaseTables({
    required this.users,
    required this.chatHistories,
    required this.subscriptions,
  });

  factory DatabaseTables.fromJson(Map<String, dynamic> json) {
    return DatabaseTables(
      users: json['users'] as int,
      chatHistories: json['chatHistories'] as int,
      subscriptions: json['subscriptions'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'users': users,
      'chatHistories': chatHistories,
      'subscriptions': subscriptions,
    };
  }

  int get totalRecords => users + chatHistories + subscriptions;
}

// Model cho Database Info API
class DatabaseInfo {
  final String databaseType;
  final String connectionString;
  final DatabaseTables tables;
  final DateTime serverTime;
  final bool canConnect;

  DatabaseInfo({
    required this.databaseType,
    required this.connectionString,
    required this.tables,
    required this.serverTime,
    required this.canConnect,
  });

  factory DatabaseInfo.fromJson(Map<String, dynamic> json) {
    return DatabaseInfo(
      databaseType: json['databaseType'] as String,
      connectionString: json['connectionString'] as String,
      tables: DatabaseTables.fromJson(json['tables'] as Map<String, dynamic>),
      serverTime: DateTime.parse(json['serverTime'] as String),
      canConnect: json['canConnect'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'databaseType': databaseType,
      'connectionString': connectionString,
      'tables': tables.toJson(),
      'serverTime': serverTime.toIso8601String(),
      'canConnect': canConnect,
    };
  }

  // Helper methods
  String get maskedConnectionString {
    final parts = connectionString.split(';');
    return parts
        .map((part) {
          if (part.toLowerCase().contains('password') ||
              part.toLowerCase().contains('pwd')) {
            return '${part.split('=')[0]}=***';
          }
          return part;
        })
        .join(';');
  }

  String get formattedServerTime {
    return '${serverTime.day}/${serverTime.month}/${serverTime.year} ${serverTime.hour}:${serverTime.minute}';
  }

  String get statusText => canConnect ? 'Kết nối thành công' : 'Lỗi kết nối';

  Color get statusColor => canConnect ? Colors.green : Colors.red;
}

// Model cho Admin Subscription từ API
class AdminSubscription {
  final int id;
  final int userId;
  final String userName;
  final String planType;
  final double amount;
  final bool isPaid;
  final DateTime? paidAt;
  final DateTime? expiresAt;
  final int chatTokensUsed;
  final int chatTokensLimit;
  final DateTime createdAt;
  final DateTime updatedAt;

  AdminSubscription({
    required this.id,
    required this.userId,
    required this.userName,
    required this.planType,
    required this.amount,
    required this.isPaid,
    this.paidAt,
    this.expiresAt,
    required this.chatTokensUsed,
    required this.chatTokensLimit,
    required this.createdAt,
    required this.updatedAt,
  });

  factory AdminSubscription.fromJson(Map<String, dynamic> json) {
    return AdminSubscription(
      id: json['id'] as int,
      userId: json['userId'] as int,
      userName: json['userName'] as String,
      planType: json['planType'] as String,
      amount: (json['amount'] as num).toDouble(),
      isPaid: json['isPaid'] as bool,
      paidAt: json['paidAt'] != null
          ? DateTime.parse(json['paidAt'] as String)
          : null,
      expiresAt: json['expiresAt'] != null
          ? DateTime.parse(json['expiresAt'] as String)
          : null,
      chatTokensUsed: json['chatTokensUsed'] as int,
      chatTokensLimit: json['chatTokensLimit'] as int,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'userName': userName,
      'planType': planType,
      'amount': amount,
      'isPaid': isPaid,
      'paidAt': paidAt?.toIso8601String(),
      'expiresAt': expiresAt?.toIso8601String(),
      'chatTokensUsed': chatTokensUsed,
      'chatTokensLimit': chatTokensLimit,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  // Helper methods
  String get normalizedPlanType => planType.toLowerCase();

  bool get isFree => normalizedPlanType == 'free';
  bool get isPremium => normalizedPlanType == 'premium';

  Color get planColor {
    switch (normalizedPlanType) {
      case 'premium':
        return Colors.amber;
      case 'free':
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }

  IconData get planIcon {
    switch (normalizedPlanType) {
      case 'premium':
        return Icons.star_rounded;
      case 'free':
        return Icons.person_outline_rounded;
      default:
        return Icons.help_outline_rounded;
    }
  }

  String get formattedAmount {
    if (amount == 0) return 'Miễn phí';
    return '${amount.toStringAsFixed(0).replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]}.')}đ';
  }

  String get formattedCreatedAt {
    return '${createdAt.day}/${createdAt.month}/${createdAt.year}';
  }

  String get formattedPaidAt {
    if (paidAt == null) return 'Chưa thanh toán';
    return '${paidAt!.day}/${paidAt!.month}/${paidAt!.year}';
  }

  String get formattedExpiresAt {
    if (expiresAt == null) return 'Không có hạn';
    return '${expiresAt!.day}/${expiresAt!.month}/${expiresAt!.year}';
  }

  String get statusText {
    if (!isPaid) return 'Chưa thanh toán';
    if (expiresAt != null && expiresAt!.isBefore(DateTime.now())) {
      return 'Đã hết hạn';
    }
    return 'Đang hoạt động';
  }

  Color get statusColor {
    if (!isPaid) return Colors.red;
    if (expiresAt != null && expiresAt!.isBefore(DateTime.now())) {
      return Colors.orange;
    }
    return Colors.green;
  }

  double get usagePercentage {
    if (chatTokensLimit == 0) return 0.0;
    return (chatTokensUsed / chatTokensLimit).clamp(0.0, 1.0);
  }

  String get usageText {
    return '$chatTokensUsed / $chatTokensLimit tokens';
  }

  bool get isNearLimit => usagePercentage >= 0.8;
  bool get isOverLimit => chatTokensUsed >= chatTokensLimit;
}

// Response wrapper cho Subscriptions API
class SubscriptionsResponse {
  final int count;
  final List<AdminSubscription> subscriptions;

  SubscriptionsResponse({required this.count, required this.subscriptions});

  factory SubscriptionsResponse.fromJson(Map<String, dynamic> json) {
    return SubscriptionsResponse(
      count: json['count'] as int,
      subscriptions: (json['subscriptions'] as List<dynamic>)
          .map(
            (subJson) =>
                AdminSubscription.fromJson(subJson as Map<String, dynamic>),
          )
          .toList(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'count': count,
      'subscriptions': subscriptions.map((sub) => sub.toJson()).toList(),
    };
  }

  // Helper methods
  List<AdminSubscription> get freeSubscriptions =>
      subscriptions.where((s) => s.isFree).toList();

  List<AdminSubscription> get premiumSubscriptions =>
      subscriptions.where((s) => s.isPremium).toList();

  List<AdminSubscription> get paidSubscriptions =>
      subscriptions.where((s) => s.isPaid).toList();

  List<AdminSubscription> get unpaidSubscriptions =>
      subscriptions.where((s) => !s.isPaid).toList();

  int get freeCount => freeSubscriptions.length;
  int get premiumCount => premiumSubscriptions.length;
  int get paidCount => paidSubscriptions.length;
  int get unpaidCount => unpaidSubscriptions.length;

  double get totalRevenue =>
      paidSubscriptions.fold(0.0, (sum, sub) => sum + sub.amount);

  String get formattedTotalRevenue {
    return '${totalRevenue.toStringAsFixed(0).replaceAllMapped(RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), (Match m) => '${m[1]}.')}đ';
  }
}
