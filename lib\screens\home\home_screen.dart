import 'package:flutter/material.dart';
import '../settings_screen.dart';
import '../../services/auth_service.dart';
import '../../constants/user_roles.dart';
import '../../models/user_model.dart';
import 'home_app_bar.dart';
import 'dashboard_content.dart';
import 'profile_tab.dart';
import 'home_bottom_nav.dart';
import 'logout_dialog.dart';

class HomeScreen extends StatefulWidget {
  final String userName;
  final String userEmail;
  final bool isPremium;
  final UserRole? userRole;

  const HomeScreen({
    super.key,
    required this.userName,
    required this.userEmail,
    this.isPremium = false,
    this.userRole,
  });

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;
  User? _currentUser;

  // Usage stats for AI chat
  final int _todayMessages = 7;
  final int _totalMessages = 156;

  @override
  void initState() {
    super.initState();
    _loadCurrentUser();
  }

  void _loadCurrentUser() async {
    final user = await AuthService.getCurrentUser();
    if (user != null) {
      setState(() {
        _currentUser = user;
      });
    }
  }

  UserRole get _userRole => widget.userRole ?? UserRole.user;
  UserRole get _actualUserRole => _currentUser?.userRole ?? _userRole;

  void _logout() => LogoutDialog.show(context);



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: HomeAppBar(
        userRole: _actualUserRole,
        userName: widget.userName,
        onLogout: _logout,
      ),
      body: IndexedStack(
        index: _selectedIndex,
        // Performance: Only build visible tab
        sizing: StackFit.loose,
        children: [
          // Wrap each tab in RepaintBoundary for better performance
          RepaintBoundary(
            child: DashboardContent(
              userName: widget.userName,
              userEmail: widget.userEmail,
              userRole: _actualUserRole,
              todayMessages: _todayMessages,
              totalMessages: _totalMessages,
            ),
          ),
          RepaintBoundary(
            child: ProfileTab(
              userName: widget.userName,
              userEmail: widget.userEmail,
              onLogout: _logout,
            ),
          ),
          RepaintBoundary(
            child: SettingsScreen(
              userName: widget.userName,
              userEmail: widget.userEmail,
              isPremium: _actualUserRole.isPremium,
              userRole: _actualUserRole,
            ),
          ),
        ],
      ),
      bottomNavigationBar: HomeBottomNav(
        currentIndex: _selectedIndex,
        onTap: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
      ),
    );
  }
}
