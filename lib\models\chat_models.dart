// Chat API Models
class ChatRequest {
  final String message;
  final String model;

  ChatRequest({
    required this.message,
    this.model = 'gpt-3.5-turbo',
  });

  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'model': model,
    };
  }
}

class ChatResponse {
  final String response;
  final int tokensUsed;
  final String model;
  final DateTime timestamp;

  ChatResponse({
    required this.response,
    required this.tokensUsed,
    required this.model,
    required this.timestamp,
  });

  factory ChatResponse.fromJson(Map<String, dynamic> json) {
    // Handle API response format: { "success": true, "data": { ... } }
    if (json.containsKey('data') && json['data'] != null) {
      final data = json['data'];
      return ChatResponse(
        response: data['response'] ?? '',
        tokensUsed: data['tokensUsed'] ?? 0,
        model: data['model'] ?? 'gpt-3.5-turbo',
        timestamp: DateTime.parse(data['timestamp'] ?? DateTime.now().toIso8601String()),
      );
    } else {
      // Fallback for direct data format
      return ChatResponse(
        response: json['response'] ?? '',
        tokensUsed: json['tokensUsed'] ?? 0,
        model: json['model'] ?? 'gpt-3.5-turbo',
        timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
      );
    }
  }
}

class ChatHistory {
  final int id;
  final String userMessage;
  final String aiResponse;
  final int tokensUsed;
  final String model;
  final DateTime createdAt;

  ChatHistory({
    required this.id,
    required this.userMessage,
    required this.aiResponse,
    required this.tokensUsed,
    required this.model,
    required this.createdAt,
  });

  factory ChatHistory.fromJson(Map<String, dynamic> json) {
    return ChatHistory(
      id: json['id'] ?? 0,
      userMessage: json['userMessage'] ?? '',
      aiResponse: json['aiResponse'] ?? '',
      tokensUsed: json['tokensUsed'] ?? 0,
      model: json['model'] ?? 'gpt-3.5-turbo',
      createdAt: DateTime.parse(json['createdAt'] ?? DateTime.now().toIso8601String()),
    );
  }
}

class ChatPermission {
  final bool hasPermission;
  final int remainingTokens;
  final String message;

  ChatPermission({
    required this.hasPermission,
    required this.remainingTokens,
    required this.message,
  });

  factory ChatPermission.fromJson(Map<String, dynamic> json) {
    return ChatPermission(
      hasPermission: json['hasPermission'] ?? false,
      remainingTokens: json['remainingTokens'] ?? 0,
      message: json['message'] ?? '',
    );
  }
}

class TokenInfo {
  final int remainingTokens;

  TokenInfo({required this.remainingTokens});

  factory TokenInfo.fromJson(Map<String, dynamic> json) {
    return TokenInfo(
      remainingTokens: json['remainingTokens'] ?? 0,
    );
  }
}

// Local chat message model for UI
class ChatMessage {
  final String message;
  final bool isUser;
  final DateTime timestamp;
  final int? tokensUsed;
  final String? model;

  ChatMessage({
    required this.message,
    required this.isUser,
    required this.timestamp,
    this.tokensUsed,
    this.model,
  });

  factory ChatMessage.fromChatResponse(String userMessage, ChatResponse response) {
    return ChatMessage(
      message: response.response,
      isUser: false,
      timestamp: response.timestamp,
      tokensUsed: response.tokensUsed,
      model: response.model,
    );
  }

  factory ChatMessage.fromUserMessage(String message) {
    return ChatMessage(
      message: message,
      isUser: true,
      timestamp: DateTime.now(),
    );
  }

  factory ChatMessage.fromChatHistory(ChatHistory history, bool isUser) {
    return ChatMessage(
      message: isUser ? history.userMessage : history.aiResponse,
      isUser: isUser,
      timestamp: history.createdAt,
      tokensUsed: isUser ? null : history.tokensUsed,
      model: isUser ? null : history.model,
    );
  }
}
