import 'package:flutter/material.dart';

class TypingIndicator extends StatefulWidget {
  final bool isVisible;

  const TypingIndicator({
    super.key,
    required this.isVisible,
  });

  @override
  State<TypingIndicator> createState() => _TypingIndicatorState();
}

class _TypingIndicatorState extends State<TypingIndicator>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    if (widget.isVisible) {
      _animationController.repeat();
    }
  }

  @override
  void didUpdateWidget(TypingIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isVisible != oldWidget.isVisible) {
      if (widget.isVisible) {
        _animationController.repeat();
      } else {
        _animationController.stop();
      }
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.isVisible) {
      return const SizedBox.shrink();
    }

    // Cache MediaQuery for performance
    final screenWidth = MediaQuery.of(context).size.width;

    return RepaintBoundary(
      child: AnimatedOpacity(
        opacity: widget.isVisible ? 1.0 : 0.0,
        duration: const Duration(milliseconds: 300),
        child: Container(
          margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // AI Avatar - Use const where possible
            const CircleAvatar(
              radius: 16,
              backgroundColor: Color(0xFFE0E0E0), // Colors.grey[300]
              child: Icon(
                Icons.smart_toy,
                size: 18,
                color: Color(0xFF616161), // Colors.grey[700]
              ),
            ),
            const SizedBox(width: 8),

            // Typing bubble
            Container(
              constraints: BoxConstraints(
                maxWidth: screenWidth * 0.75,
              ),
              decoration: const BoxDecoration(
                color: Color(0xFFF5F5F5), // Colors.grey[100]
                borderRadius: BorderRadius.all(Radius.circular(16)),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    'AI đang soạn tin nhắn',
                    style: TextStyle(
                      color: Color(0xFF757575), // Colors.grey[600]
                      fontSize: 14,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                  const SizedBox(width: 8),
                  // Optimize AnimatedBuilder with RepaintBoundary
                  RepaintBoundary(
                    child: AnimatedBuilder(
                      animation: _animation,
                      builder: (context, child) {
                        return Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            _buildDot(0),
                            const SizedBox(width: 2),
                            _buildDot(1),
                            const SizedBox(width: 2),
                            _buildDot(2),
                          ],
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
          ),
        ),
      ),
    );
  }

  Widget _buildDot(int index) {
    final delay = index * 0.2;
    final animationValue = (_animation.value - delay).clamp(0.0, 1.0);
    final opacity = (animationValue * 2).clamp(0.0, 1.0);
    
    return Container(
      width: 6,
      height: 6,
      decoration: BoxDecoration(
        color: Colors.grey[400]!.withValues(alpha: opacity),
        shape: BoxShape.circle,
      ),
    );
  }
}
