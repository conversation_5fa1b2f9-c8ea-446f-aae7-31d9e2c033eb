import 'package:flutter/material.dart';
import '../chat/chat_screen.dart';
import '../pricing_screen.dart';
import '../../constants/user_roles.dart';

class QuickActions extends StatelessWidget {
  final String userName;
  final UserRole userRole;

  const QuickActions({
    super.key,
    required this.userName,
    required this.userRole,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: () => _navigateToChat(context),
            icon: const Icon(Icons.chat),
            label: const Text('Bắt đầu chat'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => _navigateToPricing(context),
            icon: const Icon(Icons.upgrade),
            label: Text(userRole.isPremium ? 'Đã Premium' : 'Nâng cấp'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  void _navigateToChat(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ChatScreen(
          userName: userName,
          isPremium: userRole.isPremium,
          userRole: userRole,
        ),
      ),
    );
  }

  void _navigateToPricing(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const PricingScreen(),
      ),
    );
  }
}
