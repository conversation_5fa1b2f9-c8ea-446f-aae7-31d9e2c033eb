import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/payment_models.dart';
import '../models/api_response.dart';
import '../constants/api_constants.dart';
import 'auth_service.dart';

class PaymentService {
  // Make payment
  static Future<ApiResponse<PaymentResponse>> makePayment({
    required String planType,
    required double amount,
    required String paymentMethod,
    String? transactionId,
  }) async {
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        return ApiResponse.error('Không tìm thấy token xác thực');
      }

      final request = PaymentRequest(
        planType: planType,
        amount: amount,
        paymentMethod: paymentMethod,
        transactionId: transactionId,
      );

      final response = await http.post(
        Uri.parse('${ApiConstants.baseUrl}/payment'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(request.toJson()),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        final paymentResponse = PaymentResponse.fromJson(data);
        return ApiResponse.success(
          data: paymentResponse,
          message: 'Thanh toán thành công',
        );
      } else {
        return ApiResponse.error(
          data['message'] ?? 'Lỗi khi thực hiện thanh toán',
        );
      }
    } catch (e) {
      return ApiResponse.error('Lỗi kết nối: ${e.toString()}');
    }
  }

  // Get subscription status
  static Future<ApiResponse<SubscriptionStatus>> getSubscriptionStatus() async {
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        return ApiResponse.error('Không tìm thấy token xác thực');
      }

      final response = await http.get(
        Uri.parse('${ApiConstants.baseUrl}/payment/subscription-status'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        final subscriptionStatus = SubscriptionStatus.fromJson(data);
        return ApiResponse.success(
          data: subscriptionStatus,
          message: 'Thông tin subscription đã được tải thành công',
        );
      } else {
        return ApiResponse.error(
          data['message'] ?? 'Lỗi khi tải thông tin subscription',
        );
      }
    } catch (e) {
      return ApiResponse.error('Lỗi kết nối: ${e.toString()}');
    }
  }

  // Check active subscription
  static Future<ApiResponse<ActiveSubscription>> checkActiveSubscription() async {
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        return ApiResponse.error('Không tìm thấy token xác thực');
      }

      final response = await http.get(
        Uri.parse('${ApiConstants.baseUrl}/payment/check-active-subscription'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        final activeSubscription = ActiveSubscription.fromJson(data);
        return ApiResponse.success(
          data: activeSubscription,
          message: activeSubscription.message,
        );
      } else {
        return ApiResponse.error(
          data['message'] ?? 'Lỗi khi kiểm tra subscription',
        );
      }
    } catch (e) {
      return ApiResponse.error('Lỗi kết nối: ${e.toString()}');
    }
  }

  // Get available plans (for admin)
  static Future<ApiResponse<List<PlanInfo>>> getAvailablePlans() async {
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        return ApiResponse.error('Không tìm thấy token xác thực');
      }

      final response = await http.get(
        Uri.parse('${ApiConstants.baseUrl}/payment/plans'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        final List<dynamic> plansList = data['plans'] ?? [];
        final plans = plansList
            .map((item) => PlanInfo.fromJson(item))
            .toList();
        
        return ApiResponse.success(
          data: plans,
          message: 'Danh sách gói đã được tải thành công',
        );
      } else {
        return ApiResponse.error(
          data['message'] ?? 'Lỗi khi tải danh sách gói',
        );
      }
    } catch (e) {
      return ApiResponse.error('Lỗi kết nối: ${e.toString()}');
    }
  }

  // Get system analytics (for admin)
  static Future<ApiResponse<SystemAnalytics>> getSystemAnalytics() async {
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        return ApiResponse.error('Không tìm thấy token xác thực');
      }

      final response = await http.get(
        Uri.parse('${ApiConstants.baseUrl}/payment/analytics'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        final analytics = SystemAnalytics.fromJson(data);
        return ApiResponse.success(
          data: analytics,
          message: 'Thống kê hệ thống đã được tải thành công',
        );
      } else {
        return ApiResponse.error(
          data['message'] ?? 'Lỗi khi tải thống kê hệ thống',
        );
      }
    } catch (e) {
      return ApiResponse.error('Lỗi kết nối: ${e.toString()}');
    }
  }

  // Helper methods
  static Future<bool> hasActiveSubscription() async {
    final response = await checkActiveSubscription();
    return response.success && 
           response.data?.hasActiveSubscription == true;
  }

  static Future<bool> isPremiumUser() async {
    final response = await getSubscriptionStatus();
    return response.success && 
           response.data?.isPremium == true;
  }

  static Future<String> getSubscriptionInfo() async {
    final response = await getSubscriptionStatus();
    if (response.success && response.data != null) {
      final subscription = response.data!;
      if (subscription.isActive) {
        return '${subscription.planType} - ${subscription.remainingTokens} tokens còn lại';
      } else {
        return 'Không có gói đang hoạt động';
      }
    }
    return 'Không thể tải thông tin subscription';
  }

  // Format price for display
  static String formatPrice(double price) {
    if (price == 0) return 'Miễn phí';
    return '${(price / 1000).toInt()},000 VNĐ';
  }

  // Get plan color based on type
  static String getPlanColor(String planType) {
    switch (planType.toLowerCase()) {
      case 'basic':
        return '#4CAF50'; // Green
      case 'premium':
        return '#FF9800'; // Orange
      default:
        return '#9E9E9E'; // Grey
    }
  }
}
