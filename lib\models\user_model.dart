import 'package:flutter/material.dart';
import '../constants/user_roles.dart';

class User {
  final String username;
  final String role;
  final String token;
  final DateTime expiresAt;

  User({
    required this.username,
    required this.role,
    required this.token,
    required this.expiresAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      username: json['username'] ?? '',
      role: json['role'] ?? '',
      token: json['token'] ?? '',
      expiresAt: DateTime.parse(json['expiresAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'username': username,
      'role': role,
      'token': token,
      'expiresAt': expiresAt.toIso8601String(),
    };
  }

  UserRole get userRole => UserRole.fromString(role);

  bool get isPremium => userRole.isPremium;

  bool get isAdmin => userRole.isAdmin;

  bool get isUser => userRole.isUser;

  String get roleDisplayName => userRole.displayName;

  Color get roleColor => UserRole.getColor(userRole);

  IconData get roleIcon => UserRole.getIcon(userRole);

  bool hasPermission(UserRole requiredRole) =>
      userRole.hasPermission(requiredRole);

  int get messageLimit => PermissionHelper.getMessageLimit(userRole);

  String get featureDescription =>
      PermissionHelper.getFeatureDescription(userRole);

  bool get canChatUnlimited => PermissionHelper.canChatUnlimited(userRole);

  bool get canAccessAdminPanel =>
      PermissionHelper.canAccessAdminPanel(userRole);

  bool get isTokenValid => DateTime.now().isBefore(expiresAt);
}

class AuthResponse {
  final bool success;
  final String message;
  final User? data;

  AuthResponse({required this.success, required this.message, this.data});

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null ? User.fromJson(json['data']) : null,
    );
  }
}

class RegisterRequest {
  final String username;
  final String password;
  final String role;

  RegisterRequest({
    required this.username,
    required this.password,
    this.role = 'user', // Mặc định là 'user'
  });

  Map<String, dynamic> toJson() {
    return {'username': username, 'password': password, 'role': role};
  }
}

class LoginRequest {
  final String username;
  final String password;

  LoginRequest({required this.username, required this.password});

  Map<String, dynamic> toJson() {
    return {'username': username, 'password': password};
  }
}

class ChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;

  ChatMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      text: json['text'] ?? '',
      isUser: json['isUser'] ?? false,
      timestamp: DateTime.parse(json['timestamp']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'text': text,
      'isUser': isUser,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}

class ChatRequest {
  final String message;
  final String model;

  ChatRequest({required this.message, this.model = 'gpt-4o'});

  Map<String, dynamic> toJson() {
    return {'message': message, 'model': model};
  }
}

class ChatResponse {
  final bool success;
  final String message;
  final ChatData? data;

  ChatResponse({required this.success, required this.message, this.data});

  factory ChatResponse.fromJson(Map<String, dynamic> json) {
    return ChatResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null ? ChatData.fromJson(json['data']) : null,
    );
  }
}

class ChatData {
  final String response;
  final int tokensUsed;
  final String model;
  final DateTime timestamp;

  ChatData({
    required this.response,
    required this.tokensUsed,
    required this.model,
    required this.timestamp,
  });

  factory ChatData.fromJson(Map<String, dynamic> json) {
    return ChatData(
      response: json['response'] ?? '',
      tokensUsed: json['tokensUsed'] ?? 0,
      model: json['model'] ?? '',
      timestamp: DateTime.parse(json['timestamp']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'response': response,
      'tokensUsed': tokensUsed,
      'model': model,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}
