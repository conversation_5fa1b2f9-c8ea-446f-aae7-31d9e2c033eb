import 'dart:convert';
import 'dart:async';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../constants/api_constants.dart';
import '../constants/user_roles.dart';
import '../models/user_model.dart';
import '../models/chat_models.dart' as chat_models;
import '../models/api_response.dart' as api_models;
import '../utils/platform_utils.dart';
import 'auth_service.dart';

// Cache entry for response caching
class _CacheEntry {
  final String response;
  final DateTime timestamp;

  _CacheEntry(this.response, this.timestamp);
}

class ChatService {
  // Simple response cache
  static final Map<String, _CacheEntry> _responseCache = {};
  static const Duration _cacheLifetime = Duration(minutes: 2);

  // Debug logging cho development
  static void _debugLog(String message) {
    if (kDebugMode) {
      print('[ChatService] $message');
    }
  }



  // Clean up expired cache entries
  static void _cleanupCache() {
    final now = DateTime.now();
    _responseCache.removeWhere((key, entry) =>
        now.difference(entry.timestamp) > _cacheLifetime);
  }

  // Dispose resources for memory cleanup
  static void dispose() {
    _debugLog('Disposing ChatService resources...');
    _cleanupCache(); // Clean cache before clearing
    _responseCache.clear();
    _debugLog('ChatService resources disposed');
  }



  // Gửi tin nhắn chat đến AI
  static Future<ChatResponse> sendMessage({
    required String message,
    String? context,
    UserRole? userRole,
    String model = 'gpt-4o',
  }) async {
    _debugLog('Sending chat message: $message');
    _debugLog('Context: $context, UserRole: $userRole, Model: $model');

    try {
      // Thêm context vào message để AI hiểu rõ hơn
      final contextualMessage = context != null
          ? '[$context] $message'
          : message;

      // Use the new sendChatMessage method
      final apiResponse = await sendChatMessage(
        message: contextualMessage,
        model: model,
      );

      if (apiResponse.success && apiResponse.data != null) {
        final chatData = apiResponse.data!;

        // Thêm thông tin role-specific vào response nếu cần
        String enhancedResponse = chatData.response;
        if (userRole != null) {
          enhancedResponse = _enhanceResponseWithRoleInfo(chatData.response, userRole);
        }

        return ChatResponse(
          success: true,
          message: apiResponse.message,
          data: ChatData(
            response: enhancedResponse,
            tokensUsed: chatData.tokensUsed,
            model: chatData.model,
            timestamp: chatData.timestamp,
          ),
        );
      } else {
        return ChatResponse(
          success: false,
          message: apiResponse.message,
        );
      }
    } on TimeoutException catch (e) {
      _debugLog('Timeout error: $e');
      return ChatResponse(
        success: false,
        message: PlatformUtils.getNetworkErrorMessage(),
      );
    } on FormatException catch (e) {
      _debugLog('JSON parsing error: $e');
      return ChatResponse(
        success: false,
        message: 'Lỗi phân tích dữ liệu từ máy chủ',
      );
    } catch (e) {
      _debugLog('Network/Unknown error: $e');

      if (e.toString().contains('SocketException') ||
          e.toString().contains('Connection') ||
          e.toString().contains('Network')) {
        return ChatResponse(
          success: false,
          message: PlatformUtils.getSocketErrorMessage(),
        );
      }

      return ChatResponse(
        success: false,
        message: 'Lỗi kết nối: ${e.toString()}',
      );
    }
  }

  // Thêm thông tin role-specific vào AI response
  static String _enhanceResponseWithRoleInfo(
    String response,
    UserRole userRole,
  ) {
    _debugLog('Enhancing response for role: $userRole');

    if (userRole.isAdmin) {
      return '$response\n\n💎 Admin Access: Bạn có quyền truy cập đầy đủ tất cả tính năng nâng cao.';
    } else if (userRole.isPremium) {
      return '$response\n\n⭐ Premium Feature: Phản hồi chi tiết và ưu tiên hỗ trợ.';
    }

    return response;
  }

  // Validate message trước khi gửi
  static bool isValidMessage(String message) {
    final trimmed = message.trim();
    return trimmed.isNotEmpty && trimmed.length <= 1000;
  }

  // Lấy số lượng tin nhắn đã sử dụng từ storage (có thể implement sau)
  static Future<int> getMessageCount() async {
    return 0;
  }

  // Reset số lượng tin nhắn (có thể implement sau)
  static Future<void> resetMessageCount() async {
    _debugLog('Message count reset');
  }

  // NEW API METHODS - Chat API Integration

  // Send chat message using new API
  static Future<api_models.ApiResponse<chat_models.ChatResponse>> sendChatMessage({
    required String message,
    String model = 'gpt-3.5-turbo',
  }) async {
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        return api_models.ApiResponse.error('Không tìm thấy token xác thực');
      }

      final request = chat_models.ChatRequest(message: message, model: model);

      final response = await http.post(
        Uri.parse(ApiConstants.chatUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(request.toJson()),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        final chatResponse = chat_models.ChatResponse.fromJson(data);
        return api_models.ApiResponse.success(
          data: chatResponse,
          message: 'Tin nhắn đã được gửi thành công',
        );
      } else {
        return api_models.ApiResponse.error(
          data['message'] ?? 'Lỗi khi gửi tin nhắn',
        );
      }
    } catch (e) {
      return api_models.ApiResponse.error('Lỗi kết nối: ${e.toString()}');
    }
  }

  // Check chat permission
  static Future<api_models.ApiResponse<chat_models.ChatPermission>> checkChatPermission() async {
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        return api_models.ApiResponse.error('Không tìm thấy token xác thực');
      }

      final response = await http.get(
        Uri.parse(ApiConstants.chatPermissionUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        final permission = chat_models.ChatPermission.fromJson(data);
        return api_models.ApiResponse.success(
          data: permission,
          message: permission.message,
        );
      } else {
        return api_models.ApiResponse.error(
          data['message'] ?? 'Lỗi khi kiểm tra quyền chat',
        );
      }
    } catch (e) {
      return api_models.ApiResponse.error('Lỗi kết nối: ${e.toString()}');
    }
  }

  // Get chat history
  static Future<api_models.ApiResponse<List<chat_models.ChatHistory>>> getChatHistory() async {
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        return api_models.ApiResponse.error('Không tìm thấy token xác thực');
      }

      final response = await http.get(
        Uri.parse(ApiConstants.chatHistoryUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        final List<dynamic> historyList = data['history'] ?? [];
        final history = historyList
            .map((item) => chat_models.ChatHistory.fromJson(item))
            .toList();

        return api_models.ApiResponse.success(
          data: history,
          message: 'Lịch sử chat đã được tải thành công',
        );
      } else {
        return api_models.ApiResponse.error(
          data['message'] ?? 'Lỗi khi tải lịch sử chat',
        );
      }
    } catch (e) {
      return api_models.ApiResponse.error('Lỗi kết nối: ${e.toString()}');
    }
  }

  // Get remaining tokens
  static Future<api_models.ApiResponse<chat_models.TokenInfo>> getRemainingTokens() async {
    try {
      final token = await AuthService.getToken();
      if (token == null) {
        return api_models.ApiResponse.error('Không tìm thấy token xác thực');
      }

      final response = await http.get(
        Uri.parse(ApiConstants.chatTokensUrl),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        final tokenInfo = chat_models.TokenInfo.fromJson(data);
        return api_models.ApiResponse.success(
          data: tokenInfo,
          message: 'Thông tin token đã được tải thành công',
        );
      } else {
        return api_models.ApiResponse.error(
          data['message'] ?? 'Lỗi khi tải thông tin token',
        );
      }
    } catch (e) {
      return api_models.ApiResponse.error('Lỗi kết nối: ${e.toString()}');
    }
  }

  // Helper method to convert chat history to chat messages for UI
  static List<chat_models.ChatMessage> convertHistoryToMessages(List<chat_models.ChatHistory> history) {
    List<chat_models.ChatMessage> messages = [];

    for (final item in history) {
      // Add user message
      messages.add(chat_models.ChatMessage.fromChatHistory(item, true));
      // Add AI response
      messages.add(chat_models.ChatMessage.fromChatHistory(item, false));
    }

    return messages;
  }

  // Helper method to check if user can send message
  static Future<bool> canSendMessage() async {
    final permissionResponse = await checkChatPermission();
    return permissionResponse.success &&
           permissionResponse.data?.hasPermission == true;
  }

  // Helper method to get formatted remaining tokens
  static Future<String> getFormattedRemainingTokens() async {
    final tokenResponse = await getRemainingTokens();
    if (tokenResponse.success && tokenResponse.data != null) {
      final remaining = tokenResponse.data!.remainingTokens;
      if (remaining == -1) {
        return 'Không giới hạn';
      } else {
        return '$remaining tokens';
      }
    }
    return '0 tokens';
  }
}
