# Admin Screens

T<PERSON><PERSON> mục này chứa tất cả các UI screens dành riêng cho tính năng quản trị (Admin).

## 📁 Cấu trúc files:

### `admin_dashboard_screen.dart`
- **<PERSON><PERSON><PERSON> đích**: Trang tổng quan dành cho Admin
- **Tính năng**: 
  - Thống kê tổng quan hệ thống
  - Thông tin database
  - Thống kê người dùng
  - <PERSON>hao tác nhanh (Users, Subscriptions)
- **Quyền truy cập**: Chỉ Admin

### `user_management_screen.dart`
- **<PERSON><PERSON><PERSON> đích**: Trang quản lý người dùng
- **Tính năng**:
  - Xem danh sách tất cả người dùng
  - Tìm kiếm và lọc người dùng
  - Xem chi tiết thông tin người dùng
  - Thố<PERSON> kê theo role (Admin, Premium, User)
- **<PERSON><PERSON><PERSON>n truy cập**: Chỉ Admin

### `subscription_management_screen.dart`
- **<PERSON><PERSON><PERSON> đích**: Trang quản lý subscription
- **T<PERSON>h năng**:
  - Xem danh sách tất cả subscription
  - Tìm kiếm và lọc theo plan type, trạng thái
  - Xem chi tiết thông tin subscription
  - Thống kê doanh thu và usage tokens
  - Hiển thị progress bar cho chat tokens usage
- **API Endpoint**: `/Admin/subscriptions`
- **Quyền truy cập**: Chỉ Admin

## 🔗 Navigation:

- Truy cập từ `HomeScreen` → Admin Panel
- `AdminDashboardScreen` có thể navigate đến:
  - `UserManagementScreen` (via AppBar hoặc Quick Actions)
  - `SubscriptionManagementScreen` (via AppBar hoặc Quick Actions)
- Tất cả admin screens có back navigation về Home

## 🎨 UI/UX:

- Thiết kế hiện đại với gradient backgrounds
- Responsive layout cho mobile
- Loading states và error handling
- Pull-to-refresh functionality
- Floating action buttons và tooltips

## 🔒 Security:

- Chỉ hiển thị cho users có role Admin
- Bảo vệ bằng AuthService và UserRole checks
- Tất cả API calls đều có authentication headers 