import 'package:flutter/material.dart';
import 'payment_models.dart';

// UI Model for Pricing Plans
class PricingPlan {
  final String name;
  final String price;
  final String period;
  final List<String> features;
  final List<String> limitations;
  final Color color;
  final bool isPopular;
  final bool isCurrentPlan;
  final String planType;
  final int tokenLimit;
  final int durationDays;

  PricingPlan({
    required this.name,
    required this.price,
    required this.period,
    required this.features,
    this.limitations = const [],
    required this.color,
    this.isPopular = false,
    this.isCurrentPlan = false,
    required this.planType,
    required this.tokenLimit,
    this.durationDays = 30,
  });

  // Create from API PlanInfo
  factory PricingPlan.fromPlanInfo(PlanInfo planInfo, {bool isCurrentPlan = false}) {
    return PricingPlan(
      name: planInfo.name,
      price: planInfo.priceFormatted,
      period: planInfo.durationDays == 30 ? 'tháng' : '${planInfo.durationDays} ngày',
      features: planInfo.features,
      limitations: _getLimitationsForPlan(planInfo.planType),
      color: _getColorForPlan(planInfo.planType),
      isPopular: planInfo.planType == 'Premium',
      isCurrentPlan: isCurrentPlan,
      planType: planInfo.planType,
      tokenLimit: planInfo.tokenLimit,
      durationDays: planInfo.durationDays,
    );
  }

  // Create default plans (fallback if API fails)
  static List<PricingPlan> getDefaultPlans() {
    return [
      PricingPlan(
        name: 'Miễn phí',
        price: '0',
        period: 'mãi mãi',
        features: [
          '1,000 tokens/tháng',
          '3 context cơ bản',
          'Hỗ trợ email',
          'Lưu trữ 7 ngày',
        ],
        limitations: [
          'Giới hạn tokens',
          'Không có context nâng cao',
          'Không ưu tiên hỗ trợ',
        ],
        color: Colors.grey,
        isPopular: false,
        isCurrentPlan: true,
        planType: 'Free',
        tokenLimit: 1000,
        durationDays: 30,
      ),
      PricingPlan(
        name: 'Basic',
        price: '49,000',
        period: 'tháng',
        features: [
          '10,000 tokens/tháng',
          '6 context',
          'Phản hồi nhanh hơn',
          'Lưu trữ 30 ngày',
          'Hỗ trợ chat',
        ],
        limitations: [
          'Giới hạn tokens',
          'Không xuất chat',
        ],
        color: Colors.green,
        isPopular: false,
        isCurrentPlan: false,
        planType: 'Basic',
        tokenLimit: 10000,
        durationDays: 30,
      ),
      PricingPlan(
        name: 'Premium',
        price: '99,000',
        period: 'tháng',
        features: [
          'Tokens không giới hạn',
          'Tất cả 12+ context',
          'Phản hồi nhanh nhất',
          'Lưu trữ không giới hạn',
          'Xuất lịch sử chat',
          'Hỗ trợ ưu tiên 24/7',
          'Tùy chỉnh AI personality',
        ],
        limitations: [],
        color: Colors.blue,
        isPopular: true,
        isCurrentPlan: false,
        planType: 'Premium',
        tokenLimit: -1, // Unlimited
        durationDays: 30,
      ),
    ];
  }

  static List<String> _getLimitationsForPlan(String planType) {
    switch (planType.toLowerCase()) {
      case 'free':
        return [
          'Giới hạn tokens',
          'Không có context nâng cao',
          'Không ưu tiên hỗ trợ',
        ];
      case 'basic':
        return [
          'Giới hạn tokens',
          'Không xuất chat',
        ];
      case 'premium':
        return [];
      default:
        return [];
    }
  }

  static Color _getColorForPlan(String planType) {
    switch (planType.toLowerCase()) {
      case 'free':
        return Colors.grey;
      case 'basic':
        return Colors.green;
      case 'premium':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  // Get formatted token limit
  String get tokenLimitFormatted {
    if (tokenLimit == -1) {
      return 'Không giới hạn';
    } else if (tokenLimit >= 1000) {
      return '${(tokenLimit / 1000).toInt()}K tokens';
    } else {
      return '$tokenLimit tokens';
    }
  }

  // Check if plan is free
  bool get isFree => planType.toLowerCase() == 'free';

  // Check if plan is premium
  bool get isPremium => planType.toLowerCase() == 'premium';

  // Get price as double for API
  double get priceAsDouble {
    if (price == '0' || price.isEmpty) return 0.0;
    // Remove formatting and convert to double
    final cleanPrice = price.replaceAll(',', '').replaceAll(' VNĐ', '');
    return double.tryParse(cleanPrice) ?? 0.0;
  }
}

// Payment Method Model for UI
class PaymentMethod {
  final String id;
  final String name;
  final IconData icon;
  final String description;
  final bool isEnabled;

  PaymentMethod({
    required this.id,
    required this.name,
    required this.icon,
    required this.description,
    this.isEnabled = true,
  });

  static List<PaymentMethod> getAvailableMethods() {
    return [
      PaymentMethod(
        id: 'card',
        name: 'Thẻ tín dụng/ghi nợ',
        icon: Icons.credit_card,
        description: 'Visa, Mastercard, JCB',
      ),
      PaymentMethod(
        id: 'momo',
        name: 'Ví MoMo',
        icon: Icons.account_balance_wallet,
        description: 'Thanh toán qua ví điện tử MoMo',
      ),
      PaymentMethod(
        id: 'zalopay',
        name: 'ZaloPay',
        icon: Icons.payment,
        description: 'Thanh toán qua ví ZaloPay',
      ),
      PaymentMethod(
        id: 'banking',
        name: 'Internet Banking',
        icon: Icons.account_balance,
        description: 'Chuyển khoản qua ngân hàng',
      ),
    ];
  }
}
