import 'package:flutter/material.dart';
import '../constants/user_roles.dart';
import '../models/chat_models.dart' as chat_models;

class ChatUtils {
  static String formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Vừa xong';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes} phút trước';
    } else if (difference.inDays < 1) {
      return '${difference.inHours} giờ trước';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year} ${timestamp.hour}:${timestamp.minute.toString().padLeft(2, '0')}';
    }
  }

  static String getRoleWelcomeMessage(String userName, UserRole userRole) {
    final canChatUnlimited = PermissionHelper.canChatUnlimited(userRole);
    final messageLimit = PermissionHelper.getMessageLimit(userRole);
    
    if (canChatUnlimited) {
      return 'Xin chào $userName! Tôi là AI Assistant. Bạn có thể chat không giới hạn. Tôi có thể hỗ trợ gì cho bạn hôm nay?';
    } else {
      return 'Xin chào $userName! Tôi là AI Assistant. Bạn có $messageLimit tin nhắn miễn phí hôm nay. Bạn muốn tôi hỗ trợ gì?';
    }
  }

  static String createExportContent(
    List<chat_models.ChatMessage> messages,
    String selectedContext,
  ) {
    final StringBuffer buffer = StringBuffer();
    buffer.writeln('=== Lịch sử Chat AI Assistant ===');
    buffer.writeln('Ngày xuất: ${DateTime.now().toString().split('.')[0]}');
    buffer.writeln('Context: $selectedContext');
    buffer.writeln('Tổng số tin nhắn: ${messages.length}');
    buffer.writeln('');

    for (int i = 0; i < messages.length; i++) {
      final message = messages[i];
      final sender = message.isUser ? 'Bạn' : 'AI Assistant';
      final time = formatTimestamp(message.timestamp);

      buffer.writeln('[$time] $sender:');
      buffer.writeln(message.message);
      if (!message.isUser && message.tokensUsed != null) {
        buffer.writeln('(Sử dụng ${message.tokensUsed} tokens)');
      }
      buffer.writeln('');
    }

    return buffer.toString();
  }

  static void showSnackBar(
    BuildContext context,
    String message, {
    SnackBarAction? action,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        action: action,
      ),
    );
  }

  static void scrollToBottom(ScrollController scrollController) {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (scrollController.hasClients) {
        scrollController.animateTo(
          scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }
}
