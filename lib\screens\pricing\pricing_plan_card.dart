import 'package:flutter/material.dart';
import '../../models/pricing_ui_models.dart';

class PricingPlanCard extends StatelessWidget {
  final PricingPlan plan;
  final bool isSelected;
  final VoidCallback onTap;

  const PricingPlanCard({
    super.key,
    required this.plan,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            const SizedBox(height: 16),
            _buildPrice(),
            const SizedBox(height: 16),
            _buildFeatures(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        // Plan icon
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: plan.color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            _getPlanIcon(plan.planType),
            color: plan.color,
            size: 24,
          ),
        ),
        const SizedBox(width: 12),
        
        // Plan name and popular badge
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Text(
                    plan.name,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (plan.isPopular) ...[
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.orange,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Text(
                        'Phổ biến',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
              if (plan.isCurrentPlan)
                Text(
                  'Gói hiện tại',
                  style: TextStyle(
                    color: Colors.green[600],
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
            ],
          ),
        ),
        
        // Selection indicator
        Container(
          width: 24,
          height: 24,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: isSelected ? Colors.blue : Colors.grey[400]!,
              width: 2,
            ),
            color: isSelected ? Colors.blue : Colors.transparent,
          ),
          child: isSelected
              ? const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 16,
                )
              : null,
        ),
      ],
    );
  }

  Widget _buildPrice() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          plan.price == '0' ? 'Miễn phí' : '${plan.price} VNĐ',
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.bold,
            color: plan.color,
          ),
        ),
        if (plan.price != '0') ...[
          const SizedBox(width: 4),
          Text(
            '/${plan.period}',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildFeatures() {
    return Column(
      children: [
        // Show first 3 features
        ...plan.features.take(3).map((feature) => Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Row(
            children: [
              Icon(
                Icons.check_circle,
                color: Colors.green[600],
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  feature,
                  style: const TextStyle(fontSize: 14),
                ),
              ),
            ],
          ),
        )),
        
        // Show more features if available
        if (plan.features.length > 3)
          Text(
            '+${plan.features.length - 3} tính năng khác',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 12,
              fontStyle: FontStyle.italic,
            ),
          ),
      ],
    );
  }

  IconData _getPlanIcon(String planType) {
    switch (planType.toLowerCase()) {
      case 'free':
        return Icons.free_breakfast;
      case 'basic':
        return Icons.star_border;
      case 'premium':
        return Icons.star;
      default:
        return Icons.help_outline;
    }
  }
}
