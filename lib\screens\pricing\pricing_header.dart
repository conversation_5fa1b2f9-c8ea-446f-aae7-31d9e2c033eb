import 'package:flutter/material.dart';

class PricingHeader extends StatelessWidget {
  const PricingHeader({super.key});

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenHeight < 700;
    final isVerySmallScreen = screenHeight < 600 || screenWidth < 400;

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isVerySmallScreen ? 16 : 20,
        vertical: isVerySmallScreen ? 8 : (isSmallScreen ? 16 : 24),
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withValues(alpha: 0.8),
          ],
        ),
      ),
      child: SafeArea(
        bottom: false,
        child: LayoutBuilder(
          builder: (context, constraints) {
            return SingleChildScrollView(
              child: ConstrainedBox(
                constraints: BoxConstraints(
                  minHeight: constraints.maxHeight,
                ),
                child: IntrinsicHeight(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(height: isVerySmallScreen ? 4 : (isSmallScreen ? 8 : 16)),

                      // Icon - much smaller on very small screens
                      Container(
                        padding: EdgeInsets.all(isVerySmallScreen ? 8 : (isSmallScreen ? 12 : 16)),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          shape: BoxShape.circle,
                        ),
                        child: Icon(
                          Icons.workspace_premium,
                          size: isVerySmallScreen ? 24 : (isSmallScreen ? 36 : 48),
                          color: Colors.white,
                        ),
                      ),

                      SizedBox(height: isVerySmallScreen ? 6 : (isSmallScreen ? 12 : 16)),

                      // Title - much smaller on very small screens
                      Text(
                        'Nâng cấp AI Assistant',
                        style: TextStyle(
                          fontSize: isVerySmallScreen ? 18 : (isSmallScreen ? 22 : 28),
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                        textAlign: TextAlign.center,
                      ),

                      SizedBox(height: isVerySmallScreen ? 4 : (isSmallScreen ? 6 : 8)),

                      // Subtitle - much smaller on very small screens
                      Text(
                        'Chọn gói phù hợp để trải nghiệm AI tốt nhất',
                        style: TextStyle(
                          fontSize: isVerySmallScreen ? 12 : (isSmallScreen ? 14 : 16),
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                        textAlign: TextAlign.center,
                        maxLines: isVerySmallScreen ? 2 : null,
                      ),

                      SizedBox(height: isVerySmallScreen ? 8 : (isSmallScreen ? 16 : 24)),

                      // Benefits row - hide on very small screens, compact on small screens
                      if (!isVerySmallScreen) ...[
                        if (!isSmallScreen) ...[
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              _buildBenefit(
                                Icons.chat_bubble_outline,
                                'Chat không\ngiới hạn',
                              ),
                              _buildBenefit(
                                Icons.speed,
                                'Phản hồi\nnhanh hơn',
                              ),
                              _buildBenefit(
                                Icons.support_agent,
                                'Hỗ trợ\nưu tiên',
                              ),
                            ],
                          ),
                          const SizedBox(height: 20),
                        ] else ...[
                          // Compact benefits for small screens
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              _buildCompactBenefit(Icons.chat_bubble_outline, 'Chat không giới hạn'),
                              _buildCompactBenefit(Icons.speed, 'Phản hồi nhanh'),
                              _buildCompactBenefit(Icons.support_agent, 'Hỗ trợ ưu tiên'),
                            ],
                          ),
                          const SizedBox(height: 12),
                        ],
                      ] else ...[
                        // Very minimal spacing for very small screens
                        const SizedBox(height: 4),
                      ],
                    ],
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildBenefit(IconData icon, String text) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            size: 24,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          text,
          style: TextStyle(
            fontSize: 12,
            color: Colors.white.withValues(alpha: 0.9),
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildCompactBenefit(IconData icon, String text) {
    return Expanded(
      child: Column(
        children: [
          Icon(
            icon,
            size: 20,
            color: Colors.white,
          ),
          const SizedBox(height: 4),
          Text(
            text,
            style: TextStyle(
              fontSize: 10,
              color: Colors.white.withValues(alpha: 0.9),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
