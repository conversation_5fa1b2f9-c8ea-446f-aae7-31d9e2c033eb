import 'package:flutter/material.dart';
import '../../constants/user_roles.dart';

class UsageStatsCard extends StatelessWidget {
  final UserRole userRole;
  final int todayMessages;
  final int totalMessages;

  const UsageStatsCard({
    super.key,
    required this.userRole,
    this.todayMessages = 7,
    this.totalMessages = 156,
  });

  int get _messageLimit => PermissionHelper.getMessageLimit(userRole);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.analytics_outlined,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Thống kê sử dụng',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Hôm nay',
                    '$todayMessages/$_messageLimit',
                    userRole.isPremium ? Colors.green : Colors.orange,
                    userRole.isPremium ? 'Không giới hạn' : 'tin nhắn',
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildStatItem(
                    'Tổng cộng',
                    '$totalMessages',
                    Colors.blue,
                    'tin nhắn',
                  ),
                ),
              ],
            ),
            if (!userRole.isPremium) ...[
              const SizedBox(height: 12),
              LinearProgressIndicator(
                value: todayMessages / _messageLimit,
                backgroundColor: Colors.grey[300],
                valueColor: AlwaysStoppedAnimation<Color>(
                  todayMessages >= _messageLimit ? Colors.red : Colors.orange,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                todayMessages >= _messageLimit
                    ? 'Đã hết lượt chat miễn phí hôm nay'
                    : 'Còn ${_messageLimit - todayMessages} tin nhắn miễn phí',
                style: TextStyle(
                  fontSize: 12,
                  color: todayMessages >= _messageLimit
                      ? Colors.red
                      : Colors.grey[600],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, Color color, String unit) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
        const SizedBox(height: 4),
        Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(
              value,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(width: 4),
            Text(unit, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
          ],
        ),
      ],
    );
  }
}
