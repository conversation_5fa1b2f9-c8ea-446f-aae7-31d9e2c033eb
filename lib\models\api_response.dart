// Generic API Response wrapper
class ApiResponse<T> {
  final bool success;
  final String message;
  final T? data;
  final int? statusCode;

  ApiResponse({
    required this.success,
    required this.message,
    this.data,
    this.statusCode,
  });

  // Success response factory
  factory ApiResponse.success({
    required T data,
    String message = 'Success',
    int statusCode = 200,
  }) {
    return ApiResponse<T>(
      success: true,
      message: message,
      data: data,
      statusCode: statusCode,
    );
  }

  // Error response factory
  factory ApiResponse.error(
    String message, {
    int statusCode = 400,
    T? data,
  }) {
    return ApiResponse<T>(
      success: false,
      message: message,
      data: data,
      statusCode: statusCode,
    );
  }

  // From JSON factory
  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic)? fromJsonT,
  ) {
    return ApiResponse<T>(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null && fromJsonT != null 
          ? fromJsonT(json['data']) 
          : null,
      statusCode: json['statusCode'],
    );
  }

  // To JSON method
  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data,
      'statusCode': statusCode,
    };
  }

  @override
  String toString() {
    return 'ApiResponse(success: $success, message: $message, data: $data, statusCode: $statusCode)';
  }
}

// Legacy ChatResponse for backward compatibility
class ChatResponse {
  final bool success;
  final String message;
  final ChatData? data;

  ChatResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory ChatResponse.fromJson(Map<String, dynamic> json) {
    return ChatResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'] != null ? ChatData.fromJson(json['data']) : null,
    );
  }
}

class ChatData {
  final String response;
  final int tokensUsed;
  final String model;
  final DateTime timestamp;

  ChatData({
    required this.response,
    required this.tokensUsed,
    required this.model,
    required this.timestamp,
  });

  factory ChatData.fromJson(Map<String, dynamic> json) {
    return ChatData(
      response: json['response'] ?? '',
      tokensUsed: json['tokensUsed'] ?? 0,
      model: json['model'] ?? 'gpt-3.5-turbo',
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
    );
  }
}

class ChatRequest {
  final String message;
  final String model;

  ChatRequest({
    required this.message,
    this.model = 'gpt-3.5-turbo',
  });

  Map<String, dynamic> toJson() {
    return {
      'message': message,
      'model': model,
    };
  }
}
