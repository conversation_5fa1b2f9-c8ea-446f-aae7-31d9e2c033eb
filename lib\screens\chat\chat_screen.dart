import 'package:flutter/material.dart';
import '../../constants/user_roles.dart';
import '../../models/chat_models.dart' as chat_models;
import 'chat_app_bar.dart';
import 'chat_message_bubble.dart';
import 'chat_input.dart';
import 'context_selector.dart';
import 'typing_indicator.dart';
import 'chat_dialogs.dart';
import '../../utils/chat_utils.dart';
import 'chat_message_handler.dart';

class ChatScreen extends StatefulWidget {
  final String userName;
  final bool isPremium;
  final UserRole? userRole;

  const ChatScreen({
    super.key,
    required this.userName,
    this.isPremium = false,
    this.userRole,
  });

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final List<chat_models.ChatMessage> _messages = [];

  UserRole get _userRole => widget.userRole ?? UserRole.user;
  int get _messageLimit => PermissionHelper.getMessageLimit(_userRole);
  int _messageCount = 0;
  bool get _canChatUnlimited => PermissionHelper.canChatUnlimited(_userRole);

  String _selectedContext = 'Trợ lý học tập';
  bool _isTyping = false;
  final bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _addWelcomeMessage();
    ChatMessageHandler.loadRemainingTokens();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _addWelcomeMessage() {
    setState(() {
      _messages.add(
        chat_models.ChatMessage(
          message: ChatUtils.getRoleWelcomeMessage(widget.userName, _userRole),
          isUser: false,
          timestamp: DateTime.now(),
        ),
      );
    });
  }

  void _clearChat() {
    ChatDialogs.showClearChatDialog(context, () {
      setState(() {
        _messages.clear();
        _addWelcomeMessage();
      });
    });
  }

  void _exportChat() {
    if (_messages.isEmpty) {
      ChatUtils.showSnackBar(context, 'Không có tin nhắn để xuất');
      return;
    }

    final content = ChatUtils.createExportContent(_messages, _selectedContext);
    ChatUtils.showSnackBar(
      context,
      'Đã xuất ${_messages.length} tin nhắn',
      action: SnackBarAction(
        label: 'Xem',
        onPressed: () => ChatDialogs.showExportDialog(context, content),
      ),
    );
  }

  void _sendMessage() async {
    if (_messageController.text.trim().isEmpty) return;

    final userMessage = _messageController.text.trim();
    _messageController.clear();

    await ChatMessageHandler.sendMessage(
      context: context,
      message: userMessage,
      selectedContext: _selectedContext,
      userRole: _userRole,
      messages: _messages,
      onMessagesUpdated: (updatedMessages) {
        setState(() {
          _messages.clear();
          _messages.addAll(updatedMessages);
        });
      },
      onTypingChanged: (isTyping) {
        setState(() {
          _isTyping = isTyping;
        });
      },
      onScrollToBottom: () => ChatUtils.scrollToBottom(_scrollController),
      onUpdateTokens: () => ChatMessageHandler.loadRemainingTokens(),
      messageCount: _messageCount,
      messageLimit: _messageLimit,
      canChatUnlimited: _canChatUnlimited,
    );

    if (!_canChatUnlimited) {
      setState(() {
        _messageCount++;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: ChatAppBar(
        userName: widget.userName,
        userRole: _userRole,
        selectedContext: _selectedContext,
        onClearChat: _clearChat,
        onExportChat: _userRole.isPremium ? _exportChat : null,
      ),
      body: Column(
        children: [
          // Context Selector
          ContextSelector(
            selectedContext: _selectedContext,
            onContextChanged: (context) {
              setState(() {
                _selectedContext = context;
              });
            },
            userRole: _userRole,
          ),

          // Messages List - Optimized with caching
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              itemCount: _messages.length + (_isTyping ? 1 : 0),
              cacheExtent: 1000,
              addAutomaticKeepAlives: true,
              addRepaintBoundaries: true,
              itemBuilder: (context, index) {
                if (index == _messages.length && _isTyping) {
                  return const TypingIndicator(isVisible: true);
                }
                return RepaintBoundary(
                  child: ChatMessageBubble(
                    key: ValueKey(_messages[index].timestamp.millisecondsSinceEpoch),
                    message: _messages[index],
                  ),
                );
              },
            ),
          ),

          // Chat Input
          ChatInput(
            controller: _messageController,
            onSend: _sendMessage,
            isEnabled: !_isLoading,
            isLoading: _isTyping,
            hintText: 'Nhập tin nhắn...',
            remainingMessages: _canChatUnlimited ? null : (_messageLimit - _messageCount),
            canChatUnlimited: _canChatUnlimited,
          ),
        ],
      ),
    );
  }
}
