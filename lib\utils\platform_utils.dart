import 'package:flutter/foundation.dart';

/// Web-safe platform detection utility
/// Thay thế cho dart:io Platform class để tương thích với web
class PlatformUtils {
  /// Check if running on web browser
  static bool get isWeb => kIsWeb;

  /// Check if running on Android (mobile only)
  static bool get isAndroid =>
      !kIsWeb && defaultTargetPlatform == TargetPlatform.android;

  /// Check if running on iOS (mobile only)
  static bool get isIOS =>
      !kIsWeb && defaultTargetPlatform == TargetPlatform.iOS;

  /// Check if running on mobile platforms (Android or iOS)
  static bool get isMobile => isAndroid || isIOS;

  /// Check if running on desktop platforms
  static bool get isDesktop =>
      !kIsWeb &&
      (defaultTargetPlatform == TargetPlatform.windows ||
          defaultTargetPlatform == TargetPlatform.macOS ||
          defaultTargetPlatform == TargetPlatform.linux);

  /// Get platform name as string
  static String get platformName {
    if (kIsWeb) return 'web';

    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return 'android';
      case TargetPlatform.iOS:
        return 'ios';
      case TargetPlatform.windows:
        return 'windows';
      case TargetPlatform.macOS:
        return 'macos';
      case TargetPlatform.linux:
        return 'linux';
      case TargetPlatform.fuchsia:
        return 'fuchsia';
    }
  }

  /// Get user-friendly platform description
  static String get platformDescription {
    if (kIsWeb) return 'Web Browser';

    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return 'Android Device';
      case TargetPlatform.iOS:
        return 'iOS Device';
      case TargetPlatform.windows:
        return 'Windows Desktop';
      case TargetPlatform.macOS:
        return 'macOS Desktop';
      case TargetPlatform.linux:
        return 'Linux Desktop';
      case TargetPlatform.fuchsia:
        return 'Fuchsia Device';
    }
  }

  /// Get platform-specific error messages for network issues
  static String getNetworkErrorMessage() {
    if (kIsWeb) {
      return 'Lỗi kết nối web. Kiểm tra internet và CORS settings.';
    } else if (isAndroid) {
      return 'Kết nối chậm trên emulator. Vui lòng thử lại.';
    } else {
      return 'Không thể kết nối đến server. Kiểm tra kết nối internet.';
    }
  }

  /// Get platform-specific socket error messages
  static String getSocketErrorMessage() {
    if (kIsWeb) {
      return 'Web browser không thể kết nối. Kiểm tra CORS policy.';
    } else if (isAndroid) {
      return 'Emulator không thể kết nối internet. Kiểm tra network settings.';
    } else {
      return 'Không thể kết nối đến server. Kiểm tra kết nối internet.';
    }
  }
}
