import 'package:flutter/material.dart';
import '../pricing_screen.dart';
import '../../constants/user_roles.dart';

class ChatAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String userName;
  final UserRole userRole;
  final String selectedContext;
  final VoidCallback? onClearChat;
  final VoidCallback? onExportChat;

  const ChatAppBar({
    super.key,
    required this.userName,
    required this.userRole,
    required this.selectedContext,
    this.onClearChat,
    this.onExportChat,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'AI Chat',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (userRole.isPremium) ...[
                const SizedBox(width: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Colors.orange[600],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    'PRO',
                    style: TextStyle(
                      fontSize: 9,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ],
          ),
          Text(
            selectedContext,
            style: TextStyle(
              fontSize: 12,
              color: Colors.white.withValues(alpha: 0.8),
              fontWeight: FontWeight.normal,
            ),
          ),
        ],
      ),
      backgroundColor: Theme.of(context).primaryColor,
      foregroundColor: Colors.white,
      elevation: 0,
      actions: [
        // Export chat (Premium only)
        if (userRole.isPremium && onExportChat != null)
          IconButton(
            icon: const Icon(Icons.download),
            tooltip: 'Xuất lịch sử chat',
            onPressed: onExportChat,
          ),
        
        // More menu
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert),
          onSelected: (value) => _handleMenuSelection(context, value),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'clear',
              child: Row(
                children: [
                  Icon(Icons.clear_all, size: 20),
                  SizedBox(width: 12),
                  Text('Xóa lịch sử chat'),
                ],
              ),
            ),
            if (!userRole.isPremium)
              const PopupMenuItem(
                value: 'upgrade',
                child: Row(
                  children: [
                    Icon(Icons.upgrade, size: 20, color: Colors.orange),
                    SizedBox(width: 12),
                    Text('Nâng cấp Premium', style: TextStyle(color: Colors.orange)),
                  ],
                ),
              ),
            const PopupMenuItem(
              value: 'help',
              child: Row(
                children: [
                  Icon(Icons.help_outline, size: 20),
                  SizedBox(width: 12),
                  Text('Trợ giúp'),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _handleMenuSelection(BuildContext context, String value) {
    switch (value) {
      case 'clear':
        _showClearChatDialog(context);
        break;
      case 'upgrade':
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const PricingScreen(),
          ),
        );
        break;
      case 'help':
        _showHelpDialog(context);
        break;
    }
  }

  void _showClearChatDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Xóa lịch sử chat'),
          content: const Text(
            'Bạn có chắc chắn muốn xóa toàn bộ lịch sử chat? '
            'Hành động này không thể hoàn tác.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Hủy'),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.pop(context);
                onClearChat?.call();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
              ),
              child: const Text('Xóa'),
            ),
          ],
        );
      },
    );
  }

  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Trợ giúp'),
          content: const SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Cách sử dụng AI Chat:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 8),
                Text('• Chọn context phù hợp với câu hỏi của bạn'),
                Text('• Nhập câu hỏi và nhấn gửi'),
                Text('• AI sẽ trả lời dựa trên context đã chọn'),
                SizedBox(height: 12),
                Text(
                  'Gói miễn phí:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 4),
                Text('• 10 tin nhắn/ngày'),
                Text('• 3 context cơ bản'),
                SizedBox(height: 12),
                Text(
                  'Gói Premium:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                SizedBox(height: 4),
                Text('• Tin nhắn không giới hạn'),
                Text('• Tất cả 12+ context'),
                Text('• Xuất lịch sử chat'),
                Text('• Phản hồi nhanh hơn'),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Đóng'),
            ),
          ],
        );
      },
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);
}
