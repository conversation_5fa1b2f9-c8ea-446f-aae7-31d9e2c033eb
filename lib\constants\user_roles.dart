import 'package:flutter/material.dart';

enum UserRole {
  user('user', 'Người dùng thường', 0),
  premium('premium', 'Người dùng Premium', 1),
  admin('admin', 'Quản trị viên', 2);

  const UserRole(this.value, this.displayName, this.level);

  final String value;
  final String displayName;
  final int level;

  // Convert từ string sang enum (case-insensitive)
  static UserRole fromString(String role) {
    final normalizedRole = role.toLowerCase().trim();

    switch (normalizedRole) {
      case 'user':
        return UserRole.user;
      case 'premium':
        return UserRole.premium;
      case 'admin':
      case 'administrator':
        return UserRole.admin;
      default:
        // Default fallback cho các role không xác định
        return UserRole.user;
    }
  }

  // Kiểm tra quyền hạn dựa trên level
  bool hasPermission(UserRole requiredRole) {
    return level >= requiredRole.level;
  }

  // Kiể<PERSON> tra có phải premium không (premium hoặc admin)
  bool get isPremium => this == UserRole.premium || this == UserRole.admin;

  // Kiểm tra có phải admin không
  bool get isAdmin => this == UserRole.admin;

  // Kiểm tra có phải user thường không
  bool get isUser => this == UserRole.user;

  // Lấy màu sắc tương ứng với role
  static getColor(UserRole role) {
    switch (role) {
      case UserRole.user:
        return const Color(0xFF2196F3); // Blue
      case UserRole.premium:
        return const Color(0xFFFF9800); // Orange
      case UserRole.admin:
        return const Color(0xFFF44336); // Red
    }
  }

  // Lấy icon tương ứng với role
  static getIcon(UserRole role) {
    switch (role) {
      case UserRole.user:
        return Icons.person;
      case UserRole.premium:
        return Icons.star;
      case UserRole.admin:
        return Icons.admin_panel_settings;
    }
  }

  // Lấy danh sách tất cả roles để hiển thị trong dropdown
  static List<UserRole> get allRoles => UserRole.values;
}

// Utility class để xử lý permissions
class PermissionHelper {
  // Kiểm tra user có quyền truy cập chat unlimited không
  static bool canChatUnlimited(UserRole role) {
    return role.isPremium;
  }

  // Kiểm tra user có quyền truy cập admin panel không
  static bool canAccessAdminPanel(UserRole role) {
    return role.isAdmin;
  }

  // Kiểm tra user có quyền sử dụng premium features không
  static bool canUsePremiumFeatures(UserRole role) {
    return role.isPremium;
  }

  // Lấy message limit dựa trên role
  static int getMessageLimit(UserRole role) {
    switch (role) {
      case UserRole.user:
        return 10; // Free users: 10 messages
      case UserRole.premium:
      case UserRole.admin:
        return -1; // Premium/Admin: unlimited (-1 means unlimited)
    }
  }

  // Lấy feature description dựa trên role
  static String getFeatureDescription(UserRole role) {
    switch (role) {
      case UserRole.user:
        return 'Giới hạn 10 tin nhắn/ngày, AI contexts cơ bản';
      case UserRole.premium:
        return 'Chat không giới hạn, tất cả AI contexts, ưu tiên hỗ trợ';
      case UserRole.admin:
        return 'Toàn quyền truy cập, quản lý hệ thống, analytics';
    }
  }
}
