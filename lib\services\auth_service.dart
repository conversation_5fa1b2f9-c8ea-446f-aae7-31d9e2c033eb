import 'dart:convert';
import 'dart:async';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter/foundation.dart';
import '../constants/api_constants.dart';
import '../models/user_model.dart';
import '../utils/platform_utils.dart';

class AuthService {
  static const _storage = FlutterSecureStorage();

  // HTTP Client với connection pooling - Android specific configuration
  static final http.Client _client = http.Client();

  // Debug logging cho development
  static void _debugLog(String message) {
    if (kDebugMode) {
      print('[AuthService] $message');
    }
  }

  // Optimize HTTP Client cho Android Emulator
  static Future<http.Response> _makeRequest({
    required String url,
    required Map<String, String> headers,
    required String body,
    required String method,
  }) async {
    _debugLog('Making $method request to: $url');
    _debugLog('Platform: ${PlatformUtils.platformDescription}');
    _debugLog('Headers: $headers');

    for (int attempt = 0; attempt < ApiConstants.maxRetries; attempt++) {
      try {
        _debugLog('Attempt ${attempt + 1}/${ApiConstants.maxRetries}');
        final stopwatch = Stopwatch()..start();

        late http.Response response;

        if (method == 'POST') {
          response = await _client
              .post(Uri.parse(url), headers: headers, body: body)
              .timeout(
                ApiConstants.connectTimeout,
                onTimeout: () {
                  _debugLog(
                    'Request timeout after ${ApiConstants.connectTimeout.inSeconds}s',
                  );
                  throw TimeoutException(
                    'Request timeout',
                    ApiConstants.connectTimeout,
                  );
                },
              );
        }

        stopwatch.stop();
        _debugLog('Request completed in ${stopwatch.elapsedMilliseconds}ms');
        _debugLog('Response status: ${response.statusCode}');

        return response;
      } catch (e) {
        _debugLog('Request failed on attempt ${attempt + 1}: $e');

        if (attempt == ApiConstants.maxRetries - 1) {
          _debugLog('All retry attempts exhausted');
          rethrow; // Ném lỗi nếu đã hết số lần retry
        }

        // Đợi trước khi retry
        _debugLog(
          'Waiting ${ApiConstants.retryDelay.inSeconds}s before retry...',
        );
        await Future.delayed(ApiConstants.retryDelay);
      }
    }

    throw Exception('Failed after ${ApiConstants.maxRetries} attempts');
  }

  // Register API call với optimization
  static Future<AuthResponse> register({
    required String username,
    required String password,
    String role = 'user',
  }) async {
    _debugLog('Starting register for user: $username, role: $role');

    try {
      final request = RegisterRequest(
        username: username,
        password: password,
        role: role,
      );

      final response = await _makeRequest(
        url: ApiConstants.registerUrl,
        headers: ApiConstants.headers,
        body: jsonEncode(request.toJson()),
        method: 'POST',
      );

      final Map<String, dynamic> responseData = jsonDecode(response.body);
      _debugLog('Register response: $responseData');

      if (response.statusCode == 200 || response.statusCode == 201) {
        final authResponse = AuthResponse.fromJson(responseData);

        // Lưu token và user data nếu thành công
        if (authResponse.success && authResponse.data != null) {
          _debugLog('Register successful, saving user data...');
          await _saveUserData(authResponse.data!);
        }

        return authResponse;
      } else {
        return AuthResponse(
          success: false,
          message: responseData['message'] ?? 'Đăng ký thất bại',
        );
      }
    } on TimeoutException catch (e) {
      _debugLog('Timeout error: $e');
      return AuthResponse(
        success: false,
        message: PlatformUtils.getNetworkErrorMessage(),
      );
    } catch (e) {
      // Handle all network errors for web compatibility
      _debugLog('Network/Unknown error: $e');

      if (e.toString().contains('SocketException') ||
          e.toString().contains('Connection') ||
          e.toString().contains('Network')) {
        return AuthResponse(
          success: false,
          message: PlatformUtils.getSocketErrorMessage(),
        );
      }

      return AuthResponse(
        success: false,
        message: 'Lỗi kết nối: ${e.toString()}',
      );
    }
  }

  // Login API call với optimization
  static Future<AuthResponse> login({
    required String username,
    required String password,
  }) async {
    _debugLog('Starting login for user: $username');

    try {
      final request = LoginRequest(username: username, password: password);

      final response = await _makeRequest(
        url: ApiConstants.loginUrl,
        headers: ApiConstants.headers,
        body: jsonEncode(request.toJson()),
        method: 'POST',
      );

      final Map<String, dynamic> responseData = jsonDecode(response.body);
      _debugLog('Login response: $responseData');

      if (response.statusCode == 200) {
        final authResponse = AuthResponse.fromJson(responseData);

        // Lưu token và user data nếu thành công
        if (authResponse.success && authResponse.data != null) {
          _debugLog('Login successful, saving user data...');
          await _saveUserData(authResponse.data!);
        }

        return authResponse;
      } else {
        return AuthResponse(
          success: false,
          message: responseData['message'] ?? 'Đăng nhập thất bại',
        );
      }
    } on TimeoutException catch (e) {
      _debugLog('Timeout error: $e');
      return AuthResponse(
        success: false,
        message: PlatformUtils.getNetworkErrorMessage(),
      );
    } catch (e) {
      // Handle all network errors for web compatibility
      _debugLog('Network/Unknown error: $e');

      if (e.toString().contains('SocketException') ||
          e.toString().contains('Connection') ||
          e.toString().contains('Network')) {
        return AuthResponse(
          success: false,
          message: PlatformUtils.getSocketErrorMessage(),
        );
      }

      return AuthResponse(
        success: false,
        message: 'Lỗi kết nối: ${e.toString()}',
      );
    }
  }

  // Lưu user data vào secure storage (parallel writes cho tốc độ)
  static Future<void> _saveUserData(User user) async {
    _debugLog('Saving user data for: ${user.username}');
    await Future.wait([
      _storage.write(key: ApiConstants.tokenKey, value: user.token),
      _storage.write(key: ApiConstants.usernameKey, value: user.username),
      _storage.write(key: ApiConstants.roleKey, value: user.role),
      _storage.write(
        key: ApiConstants.expiresAtKey,
        value: user.expiresAt.toIso8601String(),
      ),
    ]);
    _debugLog('User data saved successfully');
  }

  // Lấy user data từ storage (parallel reads cho tốc độ)
  static Future<User?> getCurrentUser() async {
    try {
      _debugLog('Getting current user from storage...');
      final results = await Future.wait([
        _storage.read(key: ApiConstants.tokenKey),
        _storage.read(key: ApiConstants.usernameKey),
        _storage.read(key: ApiConstants.roleKey),
        _storage.read(key: ApiConstants.expiresAtKey),
      ]);

      final token = results[0];
      final username = results[1];
      final role = results[2];
      final expiresAtString = results[3];

      if (token != null &&
          username != null &&
          role != null &&
          expiresAtString != null) {
        final user = User(
          token: token,
          username: username,
          role: role,
          expiresAt: DateTime.parse(expiresAtString),
        );

        // Kiểm tra token còn hạn không
        if (user.isTokenValid) {
          _debugLog('Current user found: ${user.username}, role: ${user.role}');
          return user;
        } else {
          _debugLog('Token expired, logging out...');
          // Token hết hạn, xóa data
          await logout();
          return null;
        }
      }
      _debugLog('No current user found');
      return null;
    } catch (e) {
      _debugLog('Error getting current user: $e');
      return null;
    }
  }

  // Đăng xuất (parallel deletes cho tốc độ)
  static Future<void> logout() async {
    _debugLog('Logging out user...');
    await Future.wait([
      _storage.delete(key: ApiConstants.tokenKey),
      _storage.delete(key: ApiConstants.usernameKey),
      _storage.delete(key: ApiConstants.roleKey),
      _storage.delete(key: ApiConstants.expiresAtKey),
    ]);
    _debugLog('User logged out successfully');
  }

  // Kiểm tra đã đăng nhập chưa
  static Future<bool> isLoggedIn() async {
    final user = await getCurrentUser();
    final result = user != null;
    _debugLog('Is logged in: $result');
    return result;
  }

  // Lấy token để sử dụng cho API khác
  static Future<String?> getToken() async {
    final user = await getCurrentUser();
    return user?.token;
  }

  // Cleanup HTTP client
  static void dispose() {
    _debugLog('Disposing HTTP client...');
    _client.close();
  }
}
