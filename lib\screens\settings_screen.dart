import 'package:flutter/material.dart';
import 'pricing_screen.dart';
import '../constants/user_roles.dart';
import '../models/user_model.dart';
import '../services/auth_service.dart';

class SettingsScreen extends StatefulWidget {
  final String userName;
  final String userEmail;
  final bool isPremium;
  final UserRole? userRole;

  const SettingsScreen({
    super.key,
    required this.userName,
    required this.userEmail,
    this.isPremium = false,
    this.userRole,
  });

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _notificationsEnabled = true;
  bool _soundEnabled = true;
  String _selectedLanguage = 'vi';
  String _selectedTheme = 'system';
  User? _currentUser;

  UserRole get _userRole => widget.userRole ?? UserRole.user;

  UserRole get _actualUserRole => _currentUser?.userRole ?? _userRole;

  @override
  void initState() {
    super.initState();
    _loadCurrentUser();
  }

  void _loadCurrentUser() async {
    final user = await AuthService.getCurrentUser();
    if (user != null) {
      setState(() {
        _currentUser = user;
      });
    }
  }

  final List<Map<String, dynamic>> _usageHistory = [
    {
      'date': '2024-01-20',
      'messages': 15,
      'context': 'Trợ lý học tập',
      'duration': '45 phút',
    },
    {
      'date': '2024-01-19',
      'messages': 8,
      'context': 'Lập trình viên',
      'duration': '22 phút',
    },
    {
      'date': '2024-01-18',
      'messages': 12,
      'context': 'Dịch thuật',
      'duration': '30 phút',
    },
    {
      'date': '2024-01-17',
      'messages': 20,
      'context': 'Sáng tạo nội dung',
      'duration': '1 giờ 15 phút',
    },
    {
      'date': '2024-01-16',
      'messages': 6,
      'context': 'Tư vấn sức khỏe',
      'duration': '18 phút',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Cài đặt'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildUserInfoCard(),
            const SizedBox(height: 16),

            _buildPermissionSettings(),
            const SizedBox(height: 16),

            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  _buildSectionHeader('Cài đặt chung'),
                  _buildSwitchTile(
                    'Thông báo',
                    'Nhận thông báo từ ứng dụng',
                    Icons.notifications_outlined,
                    _notificationsEnabled,
                    (value) => setState(() => _notificationsEnabled = value),
                  ),
                  _buildSwitchTile(
                    'Âm thanh',
                    'Phát âm thanh thông báo',
                    Icons.volume_up_outlined,
                    _soundEnabled,
                    (value) => setState(() => _soundEnabled = value),
                  ),
                  _buildDropdownTile(
                    'Ngôn ngữ',
                    'Chọn ngôn ngữ hiển thị',
                    Icons.language_outlined,
                    _selectedLanguage,
                    {'vi': 'Tiếng Việt', 'en': 'English'},
                    (value) => setState(() => _selectedLanguage = value!),
                  ),
                  _buildDropdownTile(
                    'Giao diện',
                    'Chọn chế độ hiển thị',
                    Icons.palette_outlined,
                    _selectedTheme,
                    {'light': 'Sáng', 'dark': 'Tối', 'system': 'Theo hệ thống'},
                    (value) => setState(() => _selectedTheme = value!),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),

            // Usage Statistics
            _buildUsageStatistics(),
            const SizedBox(height: 24),

            // Usage History
            _buildUsageHistory(),
          ],
        ),
      ),
    );
  }

  Widget _buildUserInfoCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              UserRole.getColor(_actualUserRole).withOpacity(0.1),
              UserRole.getColor(_actualUserRole).withOpacity(0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: UserRole.getColor(_actualUserRole).withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    Icons.person,
                    size: 32,
                    color: UserRole.getColor(_actualUserRole),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.userName,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        widget.userEmail,
                        style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: UserRole.getColor(_actualUserRole).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: UserRole.getColor(_actualUserRole),
                  width: 2,
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    UserRole.getIcon(_actualUserRole),
                    color: UserRole.getColor(_actualUserRole),
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _actualUserRole.displayName,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: UserRole.getColor(_actualUserRole),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          PermissionHelper.getFeatureDescription(
                            _actualUserRole,
                          ),
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (!_actualUserRole.isPremium)
                    ElevatedButton(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const PricingScreen(),
                          ),
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.orange[600],
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                      ),
                      child: const Text('Nâng cấp'),
                    ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionSettings() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quyền hạn & Tính năng',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildPermissionItem(
              'Chat không giới hạn',
              _actualUserRole.isPremium,
              Icons.chat_bubble_outline,
            ),
            _buildPermissionItem(
              'Tất cả AI contexts',
              _actualUserRole.isPremium,
              Icons.psychology_outlined,
            ),
            _buildPermissionItem(
              'Ưu tiên hỗ trợ',
              _actualUserRole.isPremium,
              Icons.support_agent_outlined,
            ),
            if (_actualUserRole.isAdmin) ...[
              _buildPermissionItem(
                'Quản lý hệ thống',
                true,
                Icons.admin_panel_settings_outlined,
              ),
              _buildPermissionItem(
                'Analytics & Reports',
                true,
                Icons.analytics_outlined,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionItem(String title, bool hasPermission, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(
            icon,
            color: hasPermission ? Colors.green : Colors.grey,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                color: hasPermission ? Colors.black87 : Colors.grey,
                fontWeight: hasPermission ? FontWeight.w500 : FontWeight.normal,
              ),
            ),
          ),
          Icon(
            hasPermission ? Icons.check_circle : Icons.cancel,
            color: hasPermission ? Colors.green : Colors.grey,
            size: 20,
          ),
        ],
      ),
    );
  }

  Widget _buildUsageStatistics() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.analytics_outlined,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 8),
                const Text(
                  'Thống kê sử dụng',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Tin nhắn hôm nay',
                    widget.isPremium ? '∞' : '7/10',
                    Icons.chat_bubble_outline,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Tổng tin nhắn',
                    '156',
                    Icons.forum_outlined,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'Thời gian chat',
                    '12.5h',
                    Icons.access_time,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'Context yêu thích',
                    'Học tập',
                    Icons.favorite_outline,
                    Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 16, color: color),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(fontSize: 12, color: Colors.grey[600]),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUsageHistory() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.history, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                const Text(
                  'Lịch sử sử dụng',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _usageHistory.length,
              separatorBuilder: (context, index) => const Divider(height: 1),
              itemBuilder: (context, index) {
                final usage = _usageHistory[index];
                return ListTile(
                  leading: CircleAvatar(
                    backgroundColor: Theme.of(
                      context,
                    ).primaryColor.withValues(alpha: 0.1),
                    child: Text(
                      '${usage['messages']}',
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                  title: Text(usage['context']),
                  subtitle: Text('${usage['date']} • ${usage['duration']}'),
                  trailing: Text(
                    '${usage['messages']} tin nhắn',
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Text(
        title,
        style: Theme.of(
          context,
        ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    IconData icon,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return ListTile(
      leading: Icon(icon, color: Theme.of(context).primaryColor),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: Theme.of(context).primaryColor,
      ),
    );
  }

  Widget _buildDropdownTile(
    String title,
    String subtitle,
    IconData icon,
    String value,
    Map<String, String> options,
    ValueChanged<String?> onChanged,
  ) {
    return ListTile(
      leading: Icon(icon, color: Theme.of(context).primaryColor),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: DropdownButton<String>(
        value: value,
        onChanged: onChanged,
        items: options.entries.map((entry) {
          return DropdownMenuItem(value: entry.key, child: Text(entry.value));
        }).toList(),
      ),
    );
  }
}
