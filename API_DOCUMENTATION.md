# Tài Liệu API - AuthApi

## Tổng Quan
AuthApi là một RESTful API được xây dựng bằng ASP.NET Core, cung cấp các chức năng xác thực, chat <PERSON>, quản lý gói đăng ký và thanh toán.

**Base URL**: `http://localhost:8080` (development) hoặc URL production của bạn
**Swagger UI**: `{base_url}/swagger`

## Xác Thực
API sử dụng JWT Bearer Token để xác thực. Thêm header sau vào các request cần xác thực:
```
Authorization: Bearer {your_jwt_token}
```

---

## 1. Authentication Controller (`/api/auth`)

### 1.1 Đăng Ký Người Dùng
**POST** `/api/auth/register`

Đăng ký tài khoản người dùng mới.

**Request Body:**
```json
{
  "username": "string (required, max 50 chars)",
  "password": "string (required, min 6, max 100 chars)",
  "role": "string (optional, max 20 chars, default: 'User')"
}
```

**Response:**
```json
{
  "success": true,
  "message": "string",
  "data": {
    "token": "string",
    "username": "string",
    "role": "string",
    "expiresAt": "2024-01-01T00:00:00Z"
  }
}
```

**Status Codes:**
- `200 OK`: Đăng ký thành công
- `400 Bad Request`: Dữ liệu không hợp lệ hoặc lỗi đăng ký

### 1.2 Đăng Nhập
**POST** `/api/auth/login`

Đăng nhập với tên đăng nhập và mật khẩu.

**Request Body:**
```json
{
  "username": "string (required, max 50 chars)",
  "password": "string (required, min 6, max 100 chars)"
}
```

**Response:**
```json
{
  "success": true,
  "message": "string",
  "data": {
    "token": "string",
    "username": "string",
    "role": "string",
    "expiresAt": "2024-01-01T00:00:00Z"
  }
}
```

**Status Codes:**
- `200 OK`: Đăng nhập thành công
- `401 Unauthorized`: Thông tin đăng nhập không chính xác
- `400 Bad Request`: Dữ liệu không hợp lệ

---

## 2. Chat Controller (`/api/chat`)
*Yêu cầu xác thực*

### 2.1 Gửi Tin Nhắn Chat
**POST** `/api/chat/send`

Gửi tin nhắn đến AI chatbot.

**Headers:**
```
Authorization: Bearer {token}
```

**Request Body:**
```json
{
  "message": "string (required, max 2000 chars)",
  "model": "string (optional, max 50 chars, default: 'gpt-3.5-turbo')"
}
```

**Response:**
```json
{
  "success": true,
  "message": "string",
  "data": {
    "response": "string",
    "tokensUsed": 100,
    "model": "gpt-3.5-turbo",
    "timestamp": "2024-01-01T00:00:00Z"
  }
}
```

**Status Codes:**
- `200 OK`: Chat thành công
- `401 Unauthorized`: Chưa xác thực
- `400 Bad Request`: Dữ liệu không hợp lệ hoặc không đủ token

### 2.2 Lấy Lịch Sử Chat
**GET** `/api/chat/history?page={page}&pageSize={pageSize}`

Lấy lịch sử chat của người dùng hiện tại.

**Headers:**
```
Authorization: Bearer {token}
```

**Query Parameters:**
- `page`: Số trang (default: 1)
- `pageSize`: Số item mỗi trang (default: 20)

**Response:**
```json
{
  "success": true,
  "message": "string",
  "data": [
    {
      "id": 1,
      "userMessage": "string",
      "aiResponse": "string",
      "tokensUsed": 100,
      "model": "gpt-3.5-turbo",
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 2.3 Kiểm Tra Quyền Chat
**GET** `/api/chat/permission`

Kiểm tra xem người dùng có quyền sử dụng chat không.

**Headers:**
```
Authorization: Bearer {token}
```

**Response:**
```json
{
  "hasPermission": true,
  "remainingTokens": 1500,
  "message": "You have 1500 tokens remaining"
}
```

### 2.4 Lấy Số Token Còn Lại
**GET** `/api/chat/tokens`

Lấy số token còn lại của người dùng.

**Headers:**
```
Authorization: Bearer {token}
```

**Response:**
```json
{
  "remainingTokens": 1500
}
```

### 2.5 Test Kết Nối OpenAI (Admin Only)
**GET** `/api/chat/test-connection`

Test kết nối đến OpenAI API.

**Headers:**
```
Authorization: Bearer {admin_token}
```

**Response:**
```json
{
  "success": true,
  "message": "OpenAI API connection successful",
  "apiUrl": "https://api.iunhi.com",
  "testResponse": "string",
  "tokensUsed": 10
}
```

---

## 3. Payment Controller (`/api/payment`)
*Yêu cầu xác thực*

### 3.1 Xử Lý Thanh Toán
**POST** `/api/payment/process`

Xử lý thanh toán cho gói đăng ký.

**Headers:**
```
Authorization: Bearer {token}
```

**Request Body:**
```json
{
  "planType": "string (required, max 50 chars)",
  "amount": 99.99,
  "paymentMethod": "string (required, max 100 chars)",
  "transactionId": "string (optional, max 200 chars)"
}
```

**Response:**
```json
{
  "success": true,
  "message": "string",
  "data": {
    "subscriptionId": 1,
    "planType": "Premium",
    "amount": 99.99,
    "isSuccess": true,
    "paidAt": "2024-01-01T00:00:00Z",
    "expiresAt": "2024-02-01T00:00:00Z",
    "newTokenLimit": 10000
  }
}
```

### 3.2 Lấy Trạng Thái Đăng Ký
**GET** `/api/payment/subscription`

Lấy thông tin đăng ký hiện tại của người dùng.

**Headers:**
```
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "message": "string",
  "data": {
    "id": 1,
    "planType": "Premium",
    "isPaid": true,
    "paidAt": "2024-01-01T00:00:00Z",
    "expiresAt": "2024-02-01T00:00:00Z",
    "amount": 99.99,
    "chatTokensUsed": 500,
    "chatTokensLimit": 10000,
    "remainingTokens": 9500,
    "isActive": true
  }
}
```

### 3.3 Lấy Danh Sách Gói Đăng Ký
**GET** `/api/payment/plans`

Lấy danh sách các gói đăng ký có sẵn.

**Headers:**
```
Authorization: Bearer {token}
```

**Response:**
```json
{
  "success": true,
  "message": "string",
  "data": [
    {
      "name": "Basic",
      "price": 29.99,
      "tokenLimit": 5000,
      "durationDays": 30,
      "features": [
        "5,000 chat tokens",
        "Basic AI models",
        "Email support"
      ]
    },
    {
      "name": "Premium",
      "price": 99.99,
      "tokenLimit": 20000,
      "durationDays": 30,
      "features": [
        "20,000 chat tokens",
        "Advanced AI models",
        "Priority support",
        "Chat history export"
      ]
    }
  ]
}
```

### 3.4 Kiểm Tra Đăng Ký Hoạt Động
**GET** `/api/payment/active`

Kiểm tra xem người dùng có đăng ký đang hoạt động không.

**Headers:**
```
Authorization: Bearer {token}
```

**Response:**
```json
{
  "hasActiveSubscription": true,
  "message": "You have an active subscription"
}
```

### 3.5 Mô Phỏng Thanh Toán
**POST** `/api/payment/simulate/{planType}`

Mô phỏng thanh toán cho mục đích testing.

**Headers:**
```
Authorization: Bearer {token}
```

**Path Parameters:**
- `planType`: Loại gói cần mua (Basic, Premium)

**Response:**
```json
{
  "success": true,
  "message": "string",
  "data": {
    "subscriptionId": 1,
    "planType": "Premium",
    "amount": 99.99,
    "isSuccess": true,
    "paidAt": "2024-01-01T00:00:00Z",
    "expiresAt": "2024-02-01T00:00:00Z",
    "newTokenLimit": 20000
  }
}
```

### 3.6 Cập Nhật Đăng Ký (Admin Only)
**PUT** `/api/payment/admin/update-subscription?userId={userId}&planType={planType}&isPaid={isPaid}`

Cập nhật trạng thái đăng ký của người dùng.

**Headers:**
```
Authorization: Bearer {admin_token}
```

**Query Parameters:**
- `userId`: ID người dùng
- `planType`: Loại gói
- `isPaid`: Trạng thái thanh toán (true/false)

**Response:**
```json
{
  "success": true,
  "message": "string",
  "data": true
}
```

### 3.7 Lấy Danh Sách Người Dùng với Đăng Ký (Admin Only)
**GET** `/api/payment/admin/users`

Lấy danh sách tất cả người dùng cùng thông tin đăng ký.

**Headers:**
```
Authorization: Bearer {admin_token}
```

**Response:**
```json
{
  "success": true,
  "message": "string",
  "data": [
    {
      "id": 1,
      "username": "user1",
      "role": "User",
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z",
      "subscriptionId": 1,
      "planType": "Premium",
      "isPaid": true,
      "paidAt": "2024-01-01T00:00:00Z",
      "expiresAt": "2024-02-01T00:00:00Z",
      "amount": 99.99,
      "paymentMethod": "CreditCard",
      "chatTokensUsed": 500,
      "chatTokensLimit": 20000,
      "remainingTokens": 19500,
      "isActive": true,
      "totalChatMessages": 25,
      "lastChatAt": "2024-01-15T00:00:00Z"
    }
  ]
}
```

### 3.8 Lấy Thống Kê Hệ Thống (Admin Only)
**GET** `/api/payment/admin/analytics`

Lấy thống kê tổng quan của hệ thống.

**Headers:**
```
Authorization: Bearer {admin_token}
```

**Response:**
```json
{
  "success": true,
  "message": "string",
  "data": {
    "totalUsers": 150,
    "totalSubscriptions": 75,
    "activeSubscriptions": 60,
    "totalRevenue": 5000.00,
    "monthlyRevenue": 1200.00,
    "totalChatMessages": 2500,
    "totalTokensUsed": 150000,
    "lastUpdated": "2024-01-01T00:00:00Z",
    "planAnalytics": [
      {
        "planType": "Basic",
        "price": 29.99,
        "tokenLimit": 5000,
        "durationDays": 30,
        "features": ["5,000 tokens", "Basic support"],
        "totalSubscribers": 40,
        "activeSubscribers": 35,
        "totalRevenue": 1200.00,
        "monthlyRevenue": 400.00,
        "totalTokensUsed": 80000
      }
    ]
  }
}
```

---

## 4. Admin Controller (`/api/admin`)

### 4.1 Lấy Thông Tin Database
**GET** `/api/admin/database-info`

Lấy thông tin về database và số lượng record.

**Response:**
```json
{
  "databaseType": "Microsoft.EntityFrameworkCore.SqlServer",
  "connectionString": "Data Source=localhost;Initial Catalog=...",
  "tables": {
    "users": 150,
    "chatHistories": 2500,
    "subscriptions": 75
  },
  "serverTime": "2024-01-01T00:00:00Z",
  "canConnect": true
}
```

### 4.2 Lấy Danh Sách Người Dùng
**GET** `/api/admin/users`

Lấy danh sách tất cả người dùng (chỉ thông tin cơ bản).

**Response:**
```json
{
  "count": 150,
  "users": [
    {
      "id": 1,
      "username": "user1",
      "role": "User",
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z",
      "hasPassword": true
    }
  ]
}
```

### 4.3 Lấy Lịch Sử Chat
**GET** `/api/admin/chat-histories?limit={limit}`

Lấy lịch sử chat của tất cả người dùng.

**Query Parameters:**
- `limit`: Số lượng record tối đa (default: 50)

**Response:**
```json
{
  "count": 50,
  "limit": 50,
  "chats": [
    {
      "id": 1,
      "userId": 1,
      "userName": "user1",
      "userMessage": "Hello AI",
      "aiResponse": "Hello! How can I help you?",
      "tokensUsed": 25,
      "model": "gpt-3.5-turbo",
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 4.4 Lấy Danh Sách Đăng Ký
**GET** `/api/admin/subscriptions`

Lấy danh sách tất cả đăng ký.

**Response:**
```json
{
  "count": 75,
  "subscriptions": [
    {
      "id": 1,
      "userId": 1,
      "userName": "user1",
      "planType": "Premium",
      "amount": 99.99,
      "isPaid": true,
      "paidAt": "2024-01-01T00:00:00Z",
      "expiresAt": "2024-02-01T00:00:00Z",
      "chatTokensUsed": 500,
      "chatTokensLimit": 20000,
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 4.5 Thực Thi Query SQL
**POST** `/api/admin/query`

Thực thi câu lệnh SQL SELECT (chỉ cho phép SELECT).

**Request Body:**
```json
{
  "sql": "SELECT * FROM Users WHERE Role = 'Admin'"
}
```

**Response:**
```json
{
  "query": "SELECT * FROM Users WHERE Role = 'Admin'",
  "results": [
    {
      "Id": 1,
      "Username": "admin",
      "Role": "Admin",
      "CreatedAt": "2024-01-01T00:00:00Z"
    }
  ],
  "count": 1
}
```

---

## Mã Lỗi HTTP

| Status Code | Mô Tả |
|-------------|-------|
| `200 OK` | Request thành công |
| `400 Bad Request` | Dữ liệu request không hợp lệ |
| `401 Unauthorized` | Chưa xác thực hoặc token không hợp lệ |
| `403 Forbidden` | Không có quyền truy cập |
| `404 Not Found` | Không tìm thấy resource |
| `500 Internal Server Error` | Lỗi server |

---

## Cấu Trúc Response Chung

Tất cả API response đều có cấu trúc chung:

```json
{
  "success": true,
  "message": "Thông báo mô tả kết quả",
  "data": "Dữ liệu trả về (có thể là object, array hoặc primitive type)"
}
```

---

## Ví Dụ Sử Dụng

### 1. Flow Đăng Ký và Sử Dụng Chat

```bash
# 1. Đăng ký tài khoản
curl -X POST "http://localhost:8080/api/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password123",
    "role": "User"
  }'

# 2. Đăng nhập
curl -X POST "http://localhost:8080/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "password123"
  }'

# 3. Mua gói đăng ký
curl -X POST "http://localhost:8080/api/payment/simulate/Premium" \
  -H "Authorization: Bearer {token}"

# 4. Sử dụng chat
curl -X POST "http://localhost:8080/api/chat/send" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Hello AI, how are you?",
    "model": "gpt-3.5-turbo"
  }'
```

### 2. Kiểm Tra Trạng Thái Đăng Ký

```bash
# Kiểm tra đăng ký hiện tại
curl -X GET "http://localhost:8080/api/payment/subscription" \
  -H "Authorization: Bearer {token}"

# Kiểm tra token còn lại
curl -X GET "http://localhost:8080/api/chat/tokens" \
  -H "Authorization: Bearer {token}"
```

---

## Ghi Chú

1. **Xác thực**: Hầu hết các endpoint yêu cầu JWT token trong header `Authorization: Bearer {token}`
2. **Phân quyền**: Một số endpoint chỉ dành cho Admin (role = "Admin")
3. **Rate Limiting**: API có thể có giới hạn số request per minute
4. **Token Management**: JWT token có thời hạn, cần refresh khi hết hạn
5. **Error Handling**: Tất cả lỗi đều được trả về với format JSON có cấu trúc nhất quán
6. **Database**: API sử dụng MySQL làm database chính
7. **OpenAI Integration**: API tích hợp với OpenAI thông qua endpoint tùy chỉnh tại `https://api.iunhi.com`

---

*Tài liệu này được tạo tự động dựa trên source code của AuthApi. Cập nhật lần cuối: 2024-01-01* 