import 'package:flutter/material.dart';

class ChatInput extends StatefulWidget {
  final TextEditingController controller;
  final VoidCallback onSend;
  final bool isEnabled;
  final bool isLoading;
  final String? hintText;
  final int? remainingMessages;
  final bool canChatUnlimited;

  const ChatInput({
    super.key,
    required this.controller,
    required this.onSend,
    this.isEnabled = true,
    this.isLoading = false,
    this.hintText,
    this.remainingMessages,
    this.canChatUnlimited = false,
  });

  @override
  State<ChatInput> createState() => _ChatInputState();
}

class _ChatInputState extends State<ChatInput> {
  bool _hasText = false;

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onTextChanged);
    super.dispose();
  }

  void _onTextChanged() {
    final hasText = widget.controller.text.trim().isNotEmpty;
    if (hasText != _hasText) {
      setState(() {
        _hasText = hasText;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(
        16,
        12,
        16,
        MediaQuery.of(context).padding.bottom + 12,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Message limit indicator
          if (!widget.canChatUnlimited && widget.remainingMessages != null)
            _buildMessageLimitIndicator(),
          
          // Input field
          Row(
            children: [
              Expanded(
                child: Container(
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(24),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: TextField(
                    controller: widget.controller,
                    enabled: widget.isEnabled && !widget.isLoading,
                    maxLines: null,
                    minLines: 1,
                    maxLength: 1000,
                    decoration: InputDecoration(
                      hintText: widget.hintText ?? 'Nhập tin nhắn...',
                      hintStyle: TextStyle(color: Colors.grey[500]),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      counterText: '', // Hide character counter
                    ),
                    textInputAction: TextInputAction.send,
                    onSubmitted: (_) => _handleSend(),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              _buildSendButton(),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMessageLimitIndicator() {
    final remaining = widget.remainingMessages ?? 0;
    final isLow = remaining <= 2;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: isLow ? Colors.orange[50] : Colors.blue[50],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isLow ? Colors.orange[200]! : Colors.blue[200]!,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isLow ? Icons.warning_amber : Icons.info_outline,
            size: 16,
            color: isLow ? Colors.orange[700] : Colors.blue[700],
          ),
          const SizedBox(width: 4),
          Text(
            'Còn $remaining tin nhắn miễn phí',
            style: TextStyle(
              fontSize: 12,
              color: isLow ? Colors.orange[700] : Colors.blue[700],
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSendButton() {
    final canSend = _hasText && widget.isEnabled && !widget.isLoading;
    
    return Container(
      decoration: BoxDecoration(
        color: canSend 
            ? Theme.of(context).primaryColor
            : Colors.grey[300],
        shape: BoxShape.circle,
      ),
      child: IconButton(
        onPressed: canSend ? _handleSend : null,
        icon: widget.isLoading
            ? SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    canSend ? Colors.white : Colors.grey[500]!,
                  ),
                ),
              )
            : Icon(
                Icons.send,
                color: canSend ? Colors.white : Colors.grey[500],
                size: 20,
              ),
      ),
    );
  }

  void _handleSend() {
    if (_hasText && widget.isEnabled && !widget.isLoading) {
      widget.onSend();
    }
  }
}
