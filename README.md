# AI Chat Bot Assistant

Ứng dụng chat bot AI thông minh được xây dựng bằng Flutter, cho phép người dùng trò chuyện với AI trong nhiều context khác nhau với mô hình freemium.

## 🚀 Tính năng chính

### 🤖 AI Chat Bot
- **6 AI Contexts chuyên biệt**: <PERSON><PERSON><PERSON> lý học tập, <PERSON><PERSON> vấn sức khỏe, <PERSON><PERSON><PERSON> tr<PERSON>nh viên, <PERSON><PERSON><PERSON>u<PERSON>, <PERSON><PERSON><PERSON> tạo nội dung, <PERSON><PERSON> vấn tài chính
- **Giao diện chat hiện đại** với bubble messages và typing indicator
- **Hỗ trợ tiếng Việt đầy đủ** với input method tối ưu

### 💰 Mô hình Freemium
- **Free tier**: 10 tin nhắn/ngày với 3 context cơ bản
- **Premium Monthly**: Chat không giới hạn với tất cả contexts
- **Premium Yearly**: Ti<PERSON>t kiệm 17% + t<PERSON><PERSON> năng nâng cao

### 💳 Thanh toán đa dạng
- Thẻ tín dụng/ghi nợ (Visa, Mastercard, JCB)
- Ví điện tử (MoMo, ZaloPay)
- Internet Banking
- Form validation đầy đủ và bảo mật

### 📊 Quản lý & Thống kê
- **Usage tracking**: Theo dõi số tin nhắn đã sử dụng
- **Chat history**: Lưu trữ lịch sử trò chuyện
- **Settings**: Cài đặt ngôn ngữ, theme, thông báo
- **Account management**: Quản lý tài khoản và subscription

## 🛠️ Công nghệ sử dụng

- **Framework**: Flutter 3.32.4
- **Language**: Dart ^3.8.1
- **UI**: Material Design 3
- **Localization**: flutter_localizations (Tiếng Việt + English)
- **State Management**: StatefulWidget với setState
- **Navigation**: MaterialPageRoute

## 📱 Platforms hỗ trợ

- ✅ **Web** (Chrome, Firefox, Safari, Edge)
- ✅ **Android** (API 21+)
- ✅ **iOS** (iOS 12+)
- ✅ **Windows** (Windows 10+)
- ✅ **macOS** (macOS 10.14+)
- ✅ **Linux** (Ubuntu 18.04+)

## 🏗️ Cấu trúc dự án

```
lib/
├── main.dart                 # Entry point
├── login_screen.dart         # Màn hình đăng nhập
├── register_screen.dart      # Màn hình đăng ký
├── home_screen.dart          # Màn hình chính
├── chat_screen.dart          # Màn hình chat với AI
├── pricing_screen.dart       # Màn hình pricing plans
├── payment_screen.dart       # Màn hình thanh toán
└── settings_screen.dart      # Màn hình cài đặt
```

## 🚀 Cài đặt và chạy

### Yêu cầu hệ thống
- Flutter SDK 3.32.4+
- Dart SDK 3.8.1+
- Android Studio / VS Code
- Git

### Các bước cài đặt

1. **Clone repository**
```bash
git clone https://github.com/phatse/PRM_ASS.git
cd PRM_ASS
```

2. **Cài đặt dependencies**
```bash
flutter pub get
```

3. **Chạy ứng dụng**
```bash
# Web
flutter run -d chrome

# Android (cần emulator hoặc device)
flutter run -d android

# iOS (chỉ trên macOS)
flutter run -d ios
```

## 🎯 User Flow

1. **Đăng ký/Đăng nhập** → Tạo tài khoản hoặc đăng nhập
2. **Home Dashboard** → Xem usage stats và quick actions
3. **Chọn AI Context** → Chọn loại trợ lý AI phù hợp
4. **Chat với AI** → Trò chuyện trong giới hạn free tier
5. **Upgrade Premium** → Nâng cấp khi cần chat nhiều hơn
6. **Thanh toán** → Chọn phương thức và hoàn tất thanh toán

## 🔧 Cấu hình

### Localization
Ứng dụng hỗ trợ đa ngôn ngữ:
- 🇻🇳 Tiếng Việt (mặc định)
- 🇺🇸 English

### Theme
- Material Design 3
- Blue color scheme
- Light mode (Dark mode sẽ được thêm sau)

## 📝 License

Dự án này được phát triển cho mục đích học tập và nghiên cứu.

## 👨‍💻 Tác giả

**Phat SE** - [GitHub](https://github.com/phatse)

## 🤝 Đóng góp

Mọi đóng góp đều được chào đón! Hãy tạo issue hoặc pull request.

## 📞 Liên hệ

Nếu có câu hỏi hoặc góp ý, vui lòng tạo issue trên GitHub.

---

*Được xây dựng với ❤️ bằng Flutter*
