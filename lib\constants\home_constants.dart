import 'package:flutter/material.dart';

class HomeConstants {
  // AI Contexts data
  static const List<Map<String, dynamic>> aiContexts = [
    {
      'icon': Icons.school_outlined,
      'title': 'Trợ lý học tập',
      'color': Colors.blue,
      'description': '<PERSON><PERSON><PERSON><PERSON> bài tập, gi<PERSON>i thích khái niệ<PERSON>',
    },
    {
      'icon': Icons.health_and_safety_outlined,
      'title': 'Tư vấn sức khỏe',
      'color': Colors.green,
      'description': 'Tư vấn sức khỏe cơ bản',
    },
    {
      'icon': Icons.code_outlined,
      'title': 'Lập trình viên',
      'color': Colors.orange,
      'description': 'Debug code, review, tối ưu',
    },
    {
      'icon': Icons.translate_outlined,
      'title': 'Dịch thuật',
      'color': Colors.purple,
      'description': 'Dịch đa ngôn ngữ chính xác',
    },
    {
      'icon': Icons.create_outlined,
      'title': '<PERSON><PERSON><PERSON> tạo nội dung',
      'color': Colors.red,
      'description': 'Vi<PERSON><PERSON> bài, ý tưởng sáng tạo',
    },
    {
      'icon': Icons.account_balance_outlined,
      'title': 'Tư vấn tài chính',
      'color': Colors.teal,
      'description': 'Lập kế hoạch tài chính',
    },
  ];

  // Recent Activities data
  static const List<Map<String, dynamic>> recentActivities = [
    {
      'title': 'Đăng nhập thành công',
      'subtitle': 'Vừa xong',
      'icon': Icons.login,
      'color': Colors.green,
    },
    {
      'title': 'Cập nhật hồ sơ',
      'subtitle': '2 giờ trước',
      'icon': Icons.person,
      'color': Colors.blue,
    },
    {
      'title': 'Thay đổi mật khẩu',
      'subtitle': '1 ngày trước',
      'icon': Icons.lock,
      'color': Colors.orange,
    },
    {
      'title': 'Đăng xuất',
      'subtitle': '2 ngày trước',
      'icon': Icons.logout,
      'color': Colors.red,
    },
  ];
}
