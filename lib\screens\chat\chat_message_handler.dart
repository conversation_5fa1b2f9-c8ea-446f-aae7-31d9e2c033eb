import 'package:flutter/material.dart';
import '../../constants/user_roles.dart';
import '../../models/chat_models.dart' as chat_models;
import '../../services/chat_service.dart';
import 'chat_dialogs.dart';

class ChatMessageHandler {
  static Future<void> sendMessage({
    required BuildContext context,
    required String message,
    required String selectedContext,
    required UserRole userRole,
    required List<chat_models.ChatMessage> messages,
    required Function(List<chat_models.ChatMessage>) onMessagesUpdated,
    required Function(bool) onTypingChanged,
    required VoidCallback onScrollToBottom,
    required VoidCallback onUpdateTokens,
    required int messageCount,
    required int messageLimit,
    required bool canChatUnlimited,
  }) async {
    // Validate message
    if (!ChatService.isValidMessage(message)) {
      ChatDialogs.showErrorDialog(
        context,
        'Tin nhắn không hợp lệ. Vui lòng nhập tin nhắn từ 1-1000 ký tự.',
      );
      return;
    }

    if (!canChatUnlimited && messageCount >= messageLimit) {
      ChatDialogs.showUpgradeDialog(context, messageLimit);
      return;
    }

    // Add user message
    final updatedMessages = List<chat_models.ChatMessage>.from(messages);
    updatedMessages.add(
      chat_models.ChatMessage.fromUserMessage(message),
    );
    onMessagesUpdated(updatedMessages);
    onTypingChanged(true);
    onScrollToBottom();

    try {
      // Call ChatService to send message
      final chatResponse = await ChatService.sendMessage(
        message: message,
        context: selectedContext,
        userRole: userRole,
        model: 'gpt-4o',
      );

      onTypingChanged(false);

      if (chatResponse.success && chatResponse.data != null) {
        updatedMessages.add(
          chat_models.ChatMessage(
            message: chatResponse.data!.response,
            isUser: false,
            timestamp: DateTime.now(),
          ),
        );
        onMessagesUpdated(updatedMessages);
        onUpdateTokens();
      } else {
        // Check if it's an authentication error
        if (chatResponse.message.contains('đăng nhập') ||
            chatResponse.message.contains('hết hạn')) {
          if (context.mounted) {
            ChatDialogs.showAuthenticationErrorDialog(
              context,
              chatResponse.message,
            );
          }
          return;
        }

        // Show error message if API fails
        updatedMessages.add(
          chat_models.ChatMessage(
            message: chatResponse.message.isNotEmpty
                ? chatResponse.message
                : 'Xin lỗi, tôi không thể trả lời câu hỏi này. Vui lòng thử lại sau.',
            isUser: false,
            timestamp: DateTime.now(),
          ),
        );
        onMessagesUpdated(updatedMessages);
      }
    } catch (e) {
      onTypingChanged(false);
      updatedMessages.add(
        chat_models.ChatMessage(
          message: 'Đã xảy ra lỗi kết nối. Vui lòng kiểm tra mạng và thử lại.',
          isUser: false,
          timestamp: DateTime.now(),
        ),
      );
      onMessagesUpdated(updatedMessages);
    }

    onScrollToBottom();
  }

  static Future<void> loadRemainingTokens() async {
    try {
      final tokenResponse = await ChatService.getRemainingTokens();
      if (tokenResponse.success && tokenResponse.data != null) {
        // Token information loaded successfully
        // Could be used for display in UI if needed
      }
    } catch (e) {
      // Ignore error for now
    }
  }
}
