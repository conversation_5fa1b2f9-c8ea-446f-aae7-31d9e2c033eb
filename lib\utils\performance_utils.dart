import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';

/// Performance utilities for monitoring and optimization
class PerformanceUtils {
  static const bool _enableProfiling = kDebugMode;
  
  /// Measure execution time of a function
  static Future<T> measureAsync<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    if (!_enableProfiling) {
      return await operation();
    }
    
    final stopwatch = Stopwatch()..start();
    try {
      final result = await operation();
      stopwatch.stop();
      _logPerformance(operationName, stopwatch.elapsedMilliseconds);
      return result;
    } catch (e) {
      stopwatch.stop();
      _logPerformance('$operationName (ERROR)', stopwatch.elapsedMilliseconds);
      rethrow;
    }
  }
  
  /// Measure execution time of a synchronous function
  static T measureSync<T>(
    String operationName,
    T Function() operation,
  ) {
    if (!_enableProfiling) {
      return operation();
    }
    
    final stopwatch = Stopwatch()..start();
    try {
      final result = operation();
      stopwatch.stop();
      _logPerformance(operationName, stopwatch.elapsedMilliseconds);
      return result;
    } catch (e) {
      stopwatch.stop();
      _logPerformance('$operationName (ERROR)', stopwatch.elapsedMilliseconds);
      rethrow;
    }
  }
  
  static void _logPerformance(String operation, int milliseconds) {
    if (kDebugMode) {
      final color = milliseconds > 1000 ? '🔴' : 
                   milliseconds > 500 ? '🟡' : '🟢';
      print('$color [PERF] $operation: ${milliseconds}ms');
    }
  }
  
  /// Memory usage monitoring (debug only)
  static void logMemoryUsage(String context) {
    if (kDebugMode) {
      // This is a placeholder - actual memory monitoring would require
      // platform-specific implementations
      print('📊 [MEMORY] $context - Memory monitoring not implemented');
    }
  }
  
  /// Widget build performance helper
  static Widget buildWithProfiling(
    String widgetName,
    Widget Function() builder,
  ) {
    if (!_enableProfiling) {
      return builder();
    }
    
    return measureSync('Build $widgetName', builder);
  }
}

/// Mixin for widgets that want to track build performance
mixin PerformanceTrackingMixin {
  String get widgetName;
  
  Widget buildWithTracking(Widget Function() builder) {
    return PerformanceUtils.buildWithProfiling(widgetName, builder);
  }
}
