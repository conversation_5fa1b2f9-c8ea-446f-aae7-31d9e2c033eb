# Hướng Dẫn Chi Tiết - Chat & Payment API

## 📋 Tổng Quan

Tài liệu này cung cấp hướng dẫn chi tiết về hai module chính của AuthApi:
- **Chat Controller**: Quản lý chat AI với OpenAI integration
- **Payment Controller**: <PERSON><PERSON><PERSON><PERSON> lý gói đăng ký và thanh toán

---

## 🤖 Chat Controller (`/api/chat`)

### Kiến Trúc & Business Logic

#### 1. Token Management System
- **Free Plan**: 1,000 tokens mặc định
- **Basic Plan**: 10,000 tokens/tháng (99,000 VNĐ)
- **Premium Plan**: 50,000 tokens/tháng (199,000 VNĐ)
- Tokens được tính dựa trên độ dài input + output
- Hệ thống kiểm tra token trước khi gửi request

#### 2. OpenAI Integration
- **Primary API**: `https://api.iunhi.com` (custom endpoint)
- **Fallback**: Standard OpenAI API format
- **Models Support**: GPT-3.5-turbo, GPT-4
- **Timeout**: 2 phút
- **Error Handling**: Comprehensive với retry logic

#### 3. Permission System
- Free users: Luôn có quyền (giới hạn token)
- Paid users: Kiểm tra subscription active
- Admin: Full access + test endpoints

---

### API Endpoints Chi Tiết

#### 🔹 1. Gửi Tin Nhắn Chat
**POST** `/api/chat/send`

**Flow Hoạt Động:**
```mermaid
graph TD
    A[Request đến] --> B[Kiểm tra Authentication]
    B --> C[Kiểm tra Permission]
    C --> D[Kiểm tra Token còn lại]
    D --> E[Gửi request đến OpenAI]
    E --> F[Nhận response]
    F --> G[Lưu Chat History]
    G --> H[Cập nhật Token Usage]
    H --> I[Trả về Response]
```

**Request Headers:**
```http
Authorization: Bearer {jwt_token}
Content-Type: application/json
```

**Request Body:**
```json
{
  "message": "Xin chào AI, bạn có khỏe không?",
  "model": "gpt-3.5-turbo"
}
```

**Validation Rules:**
- `message`: Bắt buộc, tối đa 2,000 ký tự
- `model`: Tùy chọn, mặc định "gpt-3.5-turbo"
- Supported models: gpt-3.5-turbo, gpt-4, gpt-4-32k

**Response Success:**
```json
{
  "success": true,
  "message": "Message sent successfully",
  "data": {
    "response": "Xin chào! Tôi khỏe, cảm ơn bạn đã hỏi. Tôi có thể giúp gì cho bạn?",
    "tokensUsed": 45,
    "model": "gpt-3.5-turbo",
    "timestamp": "2024-01-01T10:30:00Z"
  }
}
```

**Error Responses:**
```json
// Không đủ token
{
  "success": false,
  "message": "You have exceeded your token limit. Please upgrade your plan or wait for next month."
}

// Không có subscription
{
  "success": false,
  "message": "You need an active subscription to use the chatbot. Please upgrade your plan."
}

// Token không đủ cho request
{
  "success": false,
  "message": "This request would use 150 tokens, but you only have 50 remaining."
}
```

**Ví Dụ Sử Dụng:**
```bash
curl -X POST "http://localhost:8080/api/chat/send" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..." \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Giải thích về machine learning",
    "model": "gpt-4"
  }'
```

---

#### 🔹 2. Lấy Lịch Sử Chat
**GET** `/api/chat/history?page=1&pageSize=20`

**Business Logic:**
- Chỉ lấy chat history của user hiện tại
- Sắp xếp theo thời gian mới nhất
- Hỗ trợ phân trang
- Không giới hạn số lượng records có thể lấy

**Query Parameters:**
- `page`: Số trang (mặc định: 1)
- `pageSize`: Số record/trang (mặc định: 20, tối đa: 100)

**Response:**
```json
{
  "success": true,
  "message": "Retrieved 15 chat records",
  "data": [
    {
      "id": 123,
      "userMessage": "Giải thích về AI",
      "aiResponse": "AI (Artificial Intelligence) là...",
      "tokensUsed": 85,
      "model": "gpt-3.5-turbo",
      "createdAt": "2024-01-01T10:30:00Z"
    }
  ]
}
```

**Ví Dụ Sử Dụng:**
```bash
# Lấy 10 chat gần nhất
curl -X GET "http://localhost:8080/api/chat/history?page=1&pageSize=10" \
  -H "Authorization: Bearer {token}"

# Lấy trang 3 với 50 records/trang
curl -X GET "http://localhost:8080/api/chat/history?page=3&pageSize=50" \
  -H "Authorization: Bearer {token}"
```

---

#### 🔹 3. Kiểm Tra Quyền Chat
**GET** `/api/chat/permission`

**Business Logic:**
- Free users: Luôn có permission (token limit áp dụng)
- Paid users: Kiểm tra subscription còn hạn
- Trả về thông tin chi tiết về token

**Response:**
```json
{
  "hasPermission": true,
  "remainingTokens": 8500,
  "message": "You have 8500 tokens remaining"
}
```

**Các Trường Hợp:**
```json
// User có subscription active
{
  "hasPermission": true,
  "remainingTokens": 8500,
  "message": "You have 8500 tokens remaining"
}

// User hết token
{
  "hasPermission": false,
  "remainingTokens": 0,
  "message": "You need an active subscription to use the chatbot"
}

// Free user
{
  "hasPermission": true,
  "remainingTokens": 750,
  "message": "You have 750 tokens remaining"
}
```

---

#### 🔹 4. Lấy Số Token Còn Lại
**GET** `/api/chat/tokens`

**Response:**
```json
{
  "remainingTokens": 8500
}
```

**Logic Tính Token:**
```csharp
// Từ ChatService.cs
remainingTokens = Math.Max(0, subscription.ChatTokensLimit - subscription.ChatTokensUsed)
```

---

#### 🔹 5. Test Kết Nối OpenAI (Admin Only)
**GET** `/api/chat/test-connection`

**Yêu Cầu:** Role = "Admin"

**Business Logic:**
1. Kiểm tra kết nối đến API endpoint
2. Gửi test message đơn giản
3. Verify response format
4. Trả về thống kê connection

**Response Success:**
```json
{
  "success": true,
  "message": "OpenAI API connection successful",
  "apiUrl": "https://api.iunhi.com",
  "testResponse": "Hello! This is a test response from the AI.",
  "tokensUsed": 12
}
```

**Response Error:**
```json
{
  "success": false,
  "message": "Failed to connect to OpenAI API",
  "apiUrl": "https://api.iunhi.com",
  "suggestion": "Please check your API key and network connection"
}
```

---

## 💰 Payment Controller (`/api/payment`)

### Kiến Trúc & Business Logic

#### 1. Plan System
```csharp
// Từ PaymentService.cs - Cấu hình các gói
"Basic" = new PlanInfo
{
    Name = "Basic",
    Price = 99000,      // 99,000 VNĐ
    TokenLimit = 10000, // 10,000 tokens/tháng
    DurationDays = 30,
    Features = [
        "10,000 tokens/month",
        "GPT-3.5 Turbo access", 
        "Basic chat history",
        "Email support"
    ]
}

"Premium" = new PlanInfo
{
    Name = "Premium", 
    Price = 199000,     // 199,000 VNĐ
    TokenLimit = 50000, // 50,000 tokens/tháng
    DurationDays = 30,
    Features = [
        "50,000 tokens/month",
        "GPT-4 access",
        "Full chat history", 
        "Priority support",
        "Custom AI personality"
    ]
}
```

#### 2. Subscription Lifecycle
```mermaid
graph TD
    A[User đăng ký] --> B[Free Plan - 1000 tokens]
    B --> C[Mua Basic/Premium]
    C --> D[Payment Processing]
    D --> E[Subscription Active]
    E --> F[Token Usage Tracking]
    F --> G[Expiration Check]
    G --> H[Auto Downgrade to Free]
```

#### 3. Payment Flow
- Hỗ trợ multiple payment methods
- Transaction tracking
- Auto-renewal (planned)
- Refund handling (planned)

---

### API Endpoints Chi Tiết

#### 🔹 1. Xử Lý Thanh Toán
**POST** `/api/payment/process`

**Business Logic:**
1. Validate plan type và amount
2. Tạo hoặc update subscription
3. Set expiration date (30 ngày)
4. Reset token usage cho chu kỳ mới
5. Lưu transaction info

**Request Body:**
```json
{
  "planType": "Premium",
  "amount": 199000,
  "paymentMethod": "CreditCard",
  "transactionId": "TXN_20240101_123456"
}
```

**Validation Rules:**
- `planType`: Phải là "Basic" hoặc "Premium"
- `amount`: Phải khớp với giá gói
- `paymentMethod`: Bắt buộc
- `transactionId`: Tùy chọn, để tracking

**Response Success:**
```json
{
  "success": true,
  "message": "Payment processed successfully",
  "data": {
    "subscriptionId": 45,
    "planType": "Premium",
    "amount": 199000,
    "isSuccess": true,
    "paidAt": "2024-01-01T10:00:00Z",
    "expiresAt": "2024-01-31T10:00:00Z",
    "newTokenLimit": 50000
  }
}
```

**Error Cases:**
```json
// Plan không hợp lệ
{
  "success": false,
  "message": "Invalid plan type"
}

// Payment processing failed
{
  "success": false,
  "message": "Payment processing failed: Insufficient funds"
}
```

---

#### 🔹 2. Lấy Trạng Thái Đăng Ký
**GET** `/api/payment/subscription`

**Business Logic:**
- Lấy subscription gần nhất của user
- Nếu chưa có, tạo Free plan mặc định
- Tính toán remaining tokens và expiration

**Response:**
```json
{
  "success": true,
  "message": "",
  "data": {
    "id": 45,
    "planType": "Premium",
    "isPaid": true,
    "paidAt": "2024-01-01T10:00:00Z",
    "expiresAt": "2024-01-31T10:00:00Z",
    "amount": 199000,
    "chatTokensUsed": 5500,
    "chatTokensLimit": 50000,
    "remainingTokens": 44500,
    "isActive": true
  }
}
```

**Calculated Fields:**
```csharp
// Từ SubscriptionStatus DTO
public int RemainingTokens => ChatTokensLimit - ChatTokensUsed;
public bool IsActive => IsPaid && ExpiresAt > DateTime.UtcNow;
```

---

#### 🔹 3. Lấy Danh Sách Gói
**GET** `/api/payment/plans`

**Response:**
```json
{
  "success": true,
  "message": "",
  "data": [
    {
      "name": "Basic",
      "price": 99000,
      "tokenLimit": 10000,
      "durationDays": 30,
      "features": [
        "10,000 tokens/month",
        "GPT-3.5 Turbo access",
        "Basic chat history",
        "Email support"
      ]
    },
    {
      "name": "Premium", 
      "price": 199000,
      "tokenLimit": 50000,
      "durationDays": 30,
      "features": [
        "50,000 tokens/month",
        "GPT-4 access",
        "Full chat history",
        "Priority support",
        "Custom AI personality"
      ]
    }
  ]
}
```

---

#### 🔹 4. Kiểm Tra Đăng Ký Hoạt Động
**GET** `/api/payment/active`

**Business Logic:**
```csharp
// Kiểm tra subscription active
var subscription = await _context.Subscriptions
    .Where(s => s.UserId == userId && s.IsPaid && s.ExpiresAt > DateTime.UtcNow)
    .AnyAsync();
```

**Response:**
```json
{
  "hasActiveSubscription": true,
  "message": "You have an active subscription"
}
```

---

#### 🔹 5. Mô Phỏng Thanh Toán (Testing)
**POST** `/api/payment/simulate/{planType}`

**Mục Đích:** Testing và demo
**Path Parameters:** `planType` = "Basic" hoặc "Premium"

**Business Logic:**
1. Tự động tạo fake transaction ID
2. Xử lý như payment thật
3. Không cần payment method thật

**Ví Dụ:**
```bash
# Mô phỏng mua gói Premium
curl -X POST "http://localhost:8080/api/payment/simulate/Premium" \
  -H "Authorization: Bearer {token}"
```

**Response:**
```json
{
  "success": true,
  "message": "Payment processed successfully",
  "data": {
    "subscriptionId": 46,
    "planType": "Premium",
    "amount": 199000,
    "isSuccess": true,
    "paidAt": "2024-01-01T10:30:00Z",
    "expiresAt": "2024-01-31T10:30:00Z",
    "newTokenLimit": 50000
  }
}
```

---

### Admin Endpoints

#### 🔹 6. Cập Nhật Đăng Ký (Admin)
**PUT** `/api/payment/admin/update-subscription`

**Query Parameters:**
- `userId`: ID người dùng
- `planType`: Loại gói (Free/Basic/Premium)
- `isPaid`: Trạng thái thanh toán

**Ví Dụ:**
```bash
curl -X PUT "http://localhost:8080/api/payment/admin/update-subscription?userId=123&planType=Premium&isPaid=true" \
  -H "Authorization: Bearer {admin_token}"
```

**Business Logic:**
```csharp
// Tìm subscription gần nhất
var subscription = await _context.Subscriptions
    .Where(s => s.UserId == userId)
    .OrderByDescending(s => s.CreatedAt)
    .FirstOrDefaultAsync();

// Update hoặc tạo mới
if (subscription == null) {
    // Tạo subscription mới
} else {
    // Update subscription hiện tại
    subscription.PlanType = planType;
    subscription.IsPaid = isPaid;
    // Set token limits theo plan
}
```

---

#### 🔹 7. Danh Sách Users với Subscription (Admin)
**GET** `/api/payment/admin/users`

**Response Structure:**
```json
{
  "success": true,
  "message": "Retrieved 150 users with subscription details",
  "data": [
    {
      "id": 1,
      "username": "user1",
      "role": "User",
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-15T00:00:00Z",
      
      // Subscription Info
      "subscriptionId": 45,
      "planType": "Premium",
      "isPaid": true,
      "paidAt": "2024-01-01T00:00:00Z",
      "expiresAt": "2024-01-31T00:00:00Z",
      "amount": 199000,
      "paymentMethod": "CreditCard",
      "chatTokensUsed": 5500,
      "chatTokensLimit": 50000,
      "remainingTokens": 44500,
      "isActive": true,
      
      // Chat Statistics
      "totalChatMessages": 125,
      "lastChatAt": "2024-01-15T09:30:00Z"
    }
  ]
}
```

**Query Logic:**
```csharp
// Join Users với Subscriptions
var usersWithSubscriptions = await (
    from user in _context.Users
    join subscription in _context.Subscriptions on user.Id equals subscription.UserId into subs
    from subscription in subs.DefaultIfEmpty()
    select new { user, subscription }
).ToListAsync();

// Lấy chat statistics riêng
var chatStats = await _context.ChatHistories
    .Where(ch => ch.UserId == item.user.Id)
    .GroupBy(ch => ch.UserId)
    .Select(g => new {
        TotalMessages = g.Count(),
        LastChatAt = g.Max(ch => ch.CreatedAt)
    })
    .FirstOrDefaultAsync();
```

---

#### 🔹 8. Thống Kê Hệ Thống (Admin)
**GET** `/api/payment/admin/analytics`

**Comprehensive Analytics:**
```json
{
  "success": true,
  "message": "System analytics retrieved successfully",
  "data": {
    // Overall Stats
    "totalUsers": 1500,
    "totalSubscriptions": 450,
    "activeSubscriptions": 380,
    "totalRevenue": 45000000,     // 45M VNĐ
    "monthlyRevenue": 8500000,    // 8.5M VNĐ
    "totalChatMessages": 25000,
    "totalTokensUsed": 1200000,
    "lastUpdated": "2024-01-15T10:00:00Z",
    
    // Plan-specific Analytics
    "planAnalytics": [
      {
        "planType": "Free",
        "price": 0,
        "tokenLimit": 1000,
        "durationDays": 0,
        "features": ["1000 tokens", "Basic chat", "Community support"],
        "totalSubscribers": 1050,
        "activeSubscribers": 1050,
        "totalRevenue": 0,
        "monthlyRevenue": 0,
        "totalTokensUsed": 450000
      },
      {
        "planType": "Basic",
        "price": 99000,
        "tokenLimit": 10000,
        "durationDays": 30,
        "features": ["10,000 tokens", "GPT-3.5", "Email support"],
        "totalSubscribers": 280,
        "activeSubscribers": 230,
        "totalRevenue": 15000000,   // 15M VNĐ
        "monthlyRevenue": 4500000,  // 4.5M VNĐ  
        "totalTokensUsed": 500000
      },
      {
        "planType": "Premium",
        "price": 199000,
        "tokenLimit": 50000,
        "durationDays": 30,
        "features": ["50,000 tokens", "GPT-4", "Priority support"],
        "totalSubscribers": 170,
        "activeSubscribers": 150,
        "totalRevenue": 30000000,   // 30M VNĐ
        "monthlyRevenue": 4000000,  // 4M VNĐ
        "totalTokensUsed": 250000
      }
    ]
  }
}
```

**Analytics Calculations:**
```csharp
// Total Revenue
var totalRevenue = await _context.Subscriptions
    .Where(s => s.IsPaid)
    .SumAsync(s => s.Amount);

// Monthly Revenue (last 30 days)
var monthlyRevenue = await _context.Subscriptions
    .Where(s => s.IsPaid && s.PaidAt >= DateTime.UtcNow.AddDays(-30))
    .SumAsync(s => s.Amount);

// Active Subscriptions
var activeSubscriptions = await _context.Subscriptions
    .CountAsync(s => s.IsPaid && s.ExpiresAt > DateTime.UtcNow);
```

---

## 🔧 Implementation Details

### Database Schema

#### ChatHistory Table
```sql
CREATE TABLE ChatHistories (
    Id INT PRIMARY KEY IDENTITY,
    UserId INT NOT NULL,
    UserMessage NVARCHAR(MAX) NOT NULL,
    AiResponse NVARCHAR(MAX) NOT NULL,
    TokensUsed INT DEFAULT 0,
    Model NVARCHAR(100) DEFAULT 'gpt-4o',
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    
    FOREIGN KEY (UserId) REFERENCES Users(Id)
);
```

#### Subscription Table
```sql
CREATE TABLE Subscriptions (
    Id INT PRIMARY KEY IDENTITY,
    UserId INT NOT NULL,
    PlanType NVARCHAR(50) DEFAULT 'Free',
    IsPaid BIT DEFAULT 0,
    PaidAt DATETIME2 NULL,
    ExpiresAt DATETIME2 NULL,
    Amount DECIMAL(18,2) DEFAULT 0,
    PaymentMethod NVARCHAR(100) DEFAULT '',
    TransactionId NVARCHAR(200) DEFAULT '',
    ChatTokensUsed INT DEFAULT 0,
    ChatTokensLimit INT DEFAULT 1000,
    CreatedAt DATETIME2 DEFAULT GETUTCDATE(),
    UpdatedAt DATETIME2 NULL,
    
    FOREIGN KEY (UserId) REFERENCES Users(Id)
);
```

### Error Handling Strategy

#### Chat Service Errors
```csharp
// Token limit exceeded
if (remainingTokens <= 0) {
    return new ApiResponse<ChatResponse> {
        Success = false,
        Message = "You have exceeded your token limit. Please upgrade your plan or wait for next month."
    };
}

// OpenAI API errors
catch (HttpRequestException ex) {
    _logger.LogError(ex, "HTTP error occurred while calling OpenAI API");
    throw new Exception($"Network error occurred: {ex.Message}", ex);
}
```

#### Payment Service Errors
```csharp
// Invalid plan
if (!_plans.ContainsKey(request.PlanType)) {
    return new ApiResponse<PaymentResponse> {
        Success = false,
        Message = "Invalid plan type"
    };
}

// Database transaction failed
catch (Exception ex) {
    return new ApiResponse<PaymentResponse> {
        Success = false,
        Message = $"Payment processing failed: {ex.Message}"
    };
}
```

---

## 🚀 Usage Examples & Best Practices

### Complete Chat Flow
```bash
#!/bin/bash

# 1. Login và lấy token
TOKEN=$(curl -s -X POST "http://localhost:8080/api/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","password":"password123"}' \
  | jq -r '.data.token')

# 2. Kiểm tra quyền chat
curl -X GET "http://localhost:8080/api/chat/permission" \
  -H "Authorization: Bearer $TOKEN"

# 3. Nếu cần, mua gói Premium
curl -X POST "http://localhost:8080/api/payment/simulate/Premium" \
  -H "Authorization: Bearer $TOKEN"

# 4. Gửi tin nhắn chat
curl -X POST "http://localhost:8080/api/chat/send" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "Giải thích về quantum computing",
    "model": "gpt-4"
  }'

# 5. Xem lịch sử chat
curl -X GET "http://localhost:8080/api/chat/history?page=1&pageSize=5" \
  -H "Authorization: Bearer $TOKEN"
```

### Admin Management Flow
```bash
#!/bin/bash

# Admin token (role = "Admin")
ADMIN_TOKEN="your_admin_jwt_token"

# 1. Xem thống kê tổng quan
curl -X GET "http://localhost:8080/api/payment/admin/analytics" \
  -H "Authorization: Bearer $ADMIN_TOKEN"

# 2. Xem danh sách users
curl -X GET "http://localhost:8080/api/payment/admin/users" \
  -H "Authorization: Bearer $ADMIN_TOKEN"

# 3. Cập nhật subscription cho user
curl -X PUT "http://localhost:8080/api/payment/admin/update-subscription?userId=123&planType=Premium&isPaid=true" \
  -H "Authorization: Bearer $ADMIN_TOKEN"

# 4. Test OpenAI connection
curl -X GET "http://localhost:8080/api/chat/test-connection" \
  -H "Authorization: Bearer $ADMIN_TOKEN"
```

### Error Handling trong Frontend
```javascript
// React/TypeScript example
const sendChatMessage = async (message: string) => {
  try {
    const response = await fetch('/api/chat/send', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ message })
    });
    
    const result = await response.json();
    
    if (!result.success) {
      // Handle specific errors
      if (result.message.includes('token limit')) {
        // Redirect to upgrade page
        router.push('/upgrade');
      } else if (result.message.includes('subscription')) {
        // Show subscription modal
        setShowSubscriptionModal(true);
      } else {
        // Generic error
        toast.error(result.message);
      }
      return;
    }
    
    // Success - display chat response
    setChatHistory(prev => [...prev, {
      userMessage: message,
      aiResponse: result.data.response,
      timestamp: new Date()
    }]);
    
  } catch (error) {
    console.error('Chat error:', error);
    toast.error('Network error occurred');
  }
};
```

---

## 📊 Performance & Monitoring

### Key Metrics to Track
1. **Chat Performance**
   - Average response time từ OpenAI
   - Token usage per user/plan
   - Error rate by model type
   - Peak usage hours

2. **Payment Performance**
   - Conversion rate Free → Paid
   - Monthly recurring revenue (MRR)
   - Churn rate by plan
   - Average tokens used per plan

3. **System Health**
   - API uptime
   - Database query performance
   - Memory usage during peak hours
   - OpenAI API rate limits

### Recommended Monitoring Setup
```bash
# Log important events
_logger.LogInformation("Chat request processed for user {UserId}, tokens used: {TokensUsed}", userId, tokensUsed);
_logger.LogWarning("User {UserId} exceeded token limit", userId);
_logger.LogError("OpenAI API error: {Error}", ex.Message);

# Metrics to track in APM
- api.chat.send.duration
- api.chat.send.tokens_used
- api.payment.process.success_rate
- openai.api.response_time
- database.query.duration
```

---

## 🔐 Security Considerations

### Authentication & Authorization
- JWT tokens với expiration time
- Role-based access control (User/Admin)
- Rate limiting per user/endpoint
- Input validation và sanitization

### Data Protection
- Sensitive data encryption at rest
- PII handling compliance
- Chat history retention policy
- Payment data security (PCI compliance)

### API Security
```csharp
// Input validation example
[StringLength(2000, ErrorMessage = "Message too long")]
public string Message { get; set; }

// Rate limiting (implement with middleware)
[RateLimit(100, TimeWindow = TimeSpan.FromHours(1))]
public async Task<ActionResult> SendMessage([FromBody] ChatRequest request)

// SQL injection prevention (EF Core handles this)
var subscription = await _context.Subscriptions
    .Where(s => s.UserId == userId) // Parameterized query
    .FirstOrDefaultAsync();
```

---

*Tài liệu này cung cấp hướng dẫn đầy đủ về Chat và Payment API. Để biết thêm chi tiết về implementation, vui lòng tham khảo source code trong thư mục Services/ và Controllers/.* 