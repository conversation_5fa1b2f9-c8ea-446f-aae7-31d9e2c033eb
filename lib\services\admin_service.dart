import 'dart:convert';
import 'dart:async';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../constants/api_constants.dart';
import '../models/admin_models.dart';
import '../utils/platform_utils.dart';

class AdminService {
  // HTTP Client với connection pooling
  static final http.Client _client = http.Client();

  // Debug logging
  static void _debugLog(String message) {
    if (kDebugMode) {
      print('[AdminService] $message');
    }
  }

  // Common request method with error handling
  static Future<http.Response> _makeRequest({
    required String url,
    required Map<String, String> headers,
    String? body,
    required String method,
  }) async {
    _debugLog('Making $method request to: $url');

    try {
      late http.Response response;
      final uri = Uri.parse(url);

      switch (method.toUpperCase()) {
        case 'GET':
          response = await _client
              .get(uri, headers: headers)
              .timeout(ApiConstants.receiveTimeout);
          break;
        case 'POST':
          response = await _client
              .post(uri, headers: headers, body: body)
              .timeout(ApiConstants.receiveTimeout);
          break;
        case 'PUT':
          response = await _client
              .put(uri, headers: headers, body: body)
              .timeout(ApiConstants.receiveTimeout);
          break;
        case 'DELETE':
          response = await _client
              .delete(uri, headers: headers)
              .timeout(ApiConstants.receiveTimeout);
          break;
        default:
          throw ArgumentError('Unsupported HTTP method: $method');
      }

      _debugLog('Response status: ${response.statusCode}');
      return response;
    } catch (e) {
      _debugLog('Request failed: $e');
      rethrow;
    }
  }

  // Fetch all users - Admin only
  static Future<AdminApiResponse<UsersResponse>> fetchAllUsers() async {
    _debugLog('Fetching all users...');

    try {
      final response = await _makeRequest(
        url: '${ApiConstants.baseUrl}/Admin/users',
        headers: ApiConstants.headers,
        method: 'GET',
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        final usersResponse = UsersResponse.fromJson(responseData);

        _debugLog('Successfully fetched ${usersResponse.count} users');
        return AdminApiResponse.success(
          data: usersResponse,
          message: 'Lấy danh sách người dùng thành công',
        );
      } else {
        final errorData = jsonDecode(response.body);
        return AdminApiResponse.error(
          message: errorData['message'] ?? 'Không thể lấy danh sách người dùng',
        );
      }
    } on TimeoutException catch (e) {
      _debugLog('Timeout error: $e');
      return AdminApiResponse.error(
        message: PlatformUtils.getNetworkErrorMessage(),
      );
    } catch (e) {
      // Handle all network errors for web compatibility
      _debugLog('Network/Unknown error: $e');

      if (e.toString().contains('SocketException') ||
          e.toString().contains('Connection') ||
          e.toString().contains('Network')) {
        return AdminApiResponse.error(
          message: PlatformUtils.getSocketErrorMessage(),
        );
      }

      return AdminApiResponse.error(
        message: 'Lỗi không xác định: ${e.toString()}',
      );
    }
  }

  // Fetch database info - Admin only
  static Future<AdminApiResponse<DatabaseInfo>> fetchDatabaseInfo() async {
    _debugLog('Fetching database info...');

    try {
      final response = await _makeRequest(
        url: '${ApiConstants.baseUrl}/Admin/database-info',
        headers: ApiConstants.headers,
        method: 'GET',
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        final databaseInfo = DatabaseInfo.fromJson(responseData);

        _debugLog('Successfully fetched database info');
        return AdminApiResponse.success(
          data: databaseInfo,
          message: 'Lấy thông tin database thành công',
        );
      } else {
        final errorData = jsonDecode(response.body);
        return AdminApiResponse.error(
          message: errorData['message'] ?? 'Không thể lấy thông tin database',
        );
      }
    } on TimeoutException catch (e) {
      _debugLog('Timeout error: $e');
      return AdminApiResponse.error(
        message: PlatformUtils.getNetworkErrorMessage(),
      );
    } catch (e) {
      // Handle all network errors for web compatibility
      _debugLog('Network/Unknown error: $e');

      if (e.toString().contains('SocketException') ||
          e.toString().contains('Connection') ||
          e.toString().contains('Network')) {
        return AdminApiResponse.error(
          message: PlatformUtils.getSocketErrorMessage(),
        );
      }

      return AdminApiResponse.error(
        message: 'Lỗi không xác định: ${e.toString()}',
      );
    }
  }

  // Fetch all subscriptions - Admin only
  static Future<AdminApiResponse<SubscriptionsResponse>>
  fetchAllSubscriptions() async {
    _debugLog('Fetching all subscriptions...');

    try {
      final response = await _makeRequest(
        url: '${ApiConstants.baseUrl}/Admin/subscriptions',
        headers: ApiConstants.headers,
        method: 'GET',
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        final subscriptionsResponse = SubscriptionsResponse.fromJson(
          responseData,
        );

        _debugLog(
          'Successfully fetched ${subscriptionsResponse.count} subscriptions',
        );
        return AdminApiResponse.success(
          data: subscriptionsResponse,
          message: 'Lấy danh sách subscription thành công',
        );
      } else {
        final errorData = jsonDecode(response.body);
        return AdminApiResponse.error(
          message:
              errorData['message'] ?? 'Không thể lấy danh sách subscription',
        );
      }
    } on TimeoutException catch (e) {
      _debugLog('Timeout error: $e');
      return AdminApiResponse.error(
        message: PlatformUtils.getNetworkErrorMessage(),
      );
    } catch (e) {
      // Handle all network errors for web compatibility
      _debugLog('Network/Unknown error: $e');

      if (e.toString().contains('SocketException') ||
          e.toString().contains('Connection') ||
          e.toString().contains('Network')) {
        return AdminApiResponse.error(
          message: PlatformUtils.getSocketErrorMessage(),
        );
      }

      return AdminApiResponse.error(
        message: 'Lỗi không xác định: ${e.toString()}',
      );
    }
  }

  // Cleanup HTTP client
  static void dispose() {
    _debugLog('Disposing HTTP client...');
    _client.close();
  }
}

// Generic response wrapper cho Admin APIs
class AdminApiResponse<T> {
  final bool success;
  final String message;
  final T? data;

  AdminApiResponse({required this.success, required this.message, this.data});

  // Named constructors for convenience
  factory AdminApiResponse.success({required T data, required String message}) {
    return AdminApiResponse(success: true, message: message, data: data);
  }

  factory AdminApiResponse.error({required String message}) {
    return AdminApiResponse(success: false, message: message, data: null);
  }

  // Helper methods
  bool get isSuccess => success;
  bool get isError => !success;
  bool get hasData => data != null;
}
