// Payment API Models
class PaymentRequest {
  final String planType;
  final double amount;
  final String paymentMethod;
  final String? transactionId;

  PaymentRequest({
    required this.planType,
    required this.amount,
    required this.paymentMethod,
    this.transactionId,
  });

  Map<String, dynamic> toJson() {
    return {
      'planType': planType,
      'amount': amount,
      'paymentMethod': paymentMethod,
      if (transactionId != null) 'transactionId': transactionId,
    };
  }
}

class PaymentResponse {
  final int subscriptionId;
  final String planType;
  final double amount;
  final bool isSuccess;
  final DateTime paidAt;
  final DateTime expiresAt;
  final int newTokenLimit;

  PaymentResponse({
    required this.subscriptionId,
    required this.planType,
    required this.amount,
    required this.isSuccess,
    required this.paidAt,
    required this.expiresAt,
    required this.newTokenLimit,
  });

  factory PaymentResponse.fromJson(Map<String, dynamic> json) {
    return PaymentResponse(
      subscriptionId: json['subscriptionId'] ?? 0,
      planType: json['planType'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      isSuccess: json['isSuccess'] ?? false,
      paidAt: DateTime.parse(json['paidAt'] ?? DateTime.now().toIso8601String()),
      expiresAt: DateTime.parse(json['expiresAt'] ?? DateTime.now().toIso8601String()),
      newTokenLimit: json['newTokenLimit'] ?? 0,
    );
  }
}

class SubscriptionStatus {
  final int id;
  final String planType;
  final bool isPaid;
  final DateTime? paidAt;
  final DateTime? expiresAt;
  final double amount;
  final int chatTokensUsed;
  final int chatTokensLimit;
  final int remainingTokens;
  final bool isActive;

  SubscriptionStatus({
    required this.id,
    required this.planType,
    required this.isPaid,
    this.paidAt,
    this.expiresAt,
    required this.amount,
    required this.chatTokensUsed,
    required this.chatTokensLimit,
    required this.remainingTokens,
    required this.isActive,
  });

  factory SubscriptionStatus.fromJson(Map<String, dynamic> json) {
    return SubscriptionStatus(
      id: json['id'] ?? 0,
      planType: json['planType'] ?? 'Free',
      isPaid: json['isPaid'] ?? false,
      paidAt: json['paidAt'] != null ? DateTime.parse(json['paidAt']) : null,
      expiresAt: json['expiresAt'] != null ? DateTime.parse(json['expiresAt']) : null,
      amount: (json['amount'] ?? 0).toDouble(),
      chatTokensUsed: json['chatTokensUsed'] ?? 0,
      chatTokensLimit: json['chatTokensLimit'] ?? 1000,
      remainingTokens: json['remainingTokens'] ?? 1000,
      isActive: json['isActive'] ?? false,
    );
  }

  bool get isPremium => planType != 'Free' && isActive;
  bool get isBasic => planType == 'Basic' && isActive;
  bool get isPremiumPlan => planType == 'Premium' && isActive;
}

class PlanInfo {
  final String name;
  final double price;
  final int tokenLimit;
  final int durationDays;
  final List<String> features;

  PlanInfo({
    required this.name,
    required this.price,
    required this.tokenLimit,
    required this.durationDays,
    required this.features,
  });

  factory PlanInfo.fromJson(Map<String, dynamic> json) {
    return PlanInfo(
      name: json['name'] ?? '',
      price: (json['price'] ?? 0).toDouble(),
      tokenLimit: json['tokenLimit'] ?? 0,
      durationDays: json['durationDays'] ?? 30,
      features: List<String>.from(json['features'] ?? []),
    );
  }

  // Convert price from VND to display format
  String get priceFormatted {
    if (price == 0) return 'Miễn phí';
    return '${(price / 1000).toInt()},000 VNĐ';
  }

  // Get plan type for API
  String get planType {
    switch (name.toLowerCase()) {
      case 'basic':
        return 'Basic';
      case 'premium':
        return 'Premium';
      default:
        return 'Free';
    }
  }
}

class ActiveSubscription {
  final bool hasActiveSubscription;
  final String message;

  ActiveSubscription({
    required this.hasActiveSubscription,
    required this.message,
  });

  factory ActiveSubscription.fromJson(Map<String, dynamic> json) {
    return ActiveSubscription(
      hasActiveSubscription: json['hasActiveSubscription'] ?? false,
      message: json['message'] ?? '',
    );
  }
}

// Analytics models for admin
class SystemAnalytics {
  final int totalUsers;
  final int totalSubscriptions;
  final int activeSubscriptions;
  final double totalRevenue;
  final double monthlyRevenue;
  final int totalChatMessages;
  final int totalTokensUsed;
  final DateTime lastUpdated;
  final List<PlanAnalytics> planAnalytics;

  SystemAnalytics({
    required this.totalUsers,
    required this.totalSubscriptions,
    required this.activeSubscriptions,
    required this.totalRevenue,
    required this.monthlyRevenue,
    required this.totalChatMessages,
    required this.totalTokensUsed,
    required this.lastUpdated,
    required this.planAnalytics,
  });

  factory SystemAnalytics.fromJson(Map<String, dynamic> json) {
    return SystemAnalytics(
      totalUsers: json['totalUsers'] ?? 0,
      totalSubscriptions: json['totalSubscriptions'] ?? 0,
      activeSubscriptions: json['activeSubscriptions'] ?? 0,
      totalRevenue: (json['totalRevenue'] ?? 0).toDouble(),
      monthlyRevenue: (json['monthlyRevenue'] ?? 0).toDouble(),
      totalChatMessages: json['totalChatMessages'] ?? 0,
      totalTokensUsed: json['totalTokensUsed'] ?? 0,
      lastUpdated: DateTime.parse(json['lastUpdated'] ?? DateTime.now().toIso8601String()),
      planAnalytics: (json['planAnalytics'] as List<dynamic>?)
          ?.map((item) => PlanAnalytics.fromJson(item))
          .toList() ?? [],
    );
  }
}

class PlanAnalytics {
  final String planType;
  final double price;
  final int tokenLimit;
  final int durationDays;
  final List<String> features;
  final int totalSubscribers;
  final int activeSubscribers;
  final double totalRevenue;
  final double monthlyRevenue;
  final int totalTokensUsed;

  PlanAnalytics({
    required this.planType,
    required this.price,
    required this.tokenLimit,
    required this.durationDays,
    required this.features,
    required this.totalSubscribers,
    required this.activeSubscribers,
    required this.totalRevenue,
    required this.monthlyRevenue,
    required this.totalTokensUsed,
  });

  factory PlanAnalytics.fromJson(Map<String, dynamic> json) {
    return PlanAnalytics(
      planType: json['planType'] ?? '',
      price: (json['price'] ?? 0).toDouble(),
      tokenLimit: json['tokenLimit'] ?? 0,
      durationDays: json['durationDays'] ?? 30,
      features: List<String>.from(json['features'] ?? []),
      totalSubscribers: json['totalSubscribers'] ?? 0,
      activeSubscribers: json['activeSubscribers'] ?? 0,
      totalRevenue: (json['totalRevenue'] ?? 0).toDouble(),
      monthlyRevenue: (json['monthlyRevenue'] ?? 0).toDouble(),
      totalTokensUsed: json['totalTokensUsed'] ?? 0,
    );
  }
}
