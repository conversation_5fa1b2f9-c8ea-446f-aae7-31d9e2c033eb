import '../utils/platform_utils.dart';

class ApiConstants {
  // Base URL
  static const String baseUrl =
      'https://authapi-production-db2d.up.railway.app/api';

  // Auth Endpoints
  static const String registerEndpoint = '/Auth/register';
  static const String loginEndpoint = '/Auth/login';

  // Chat Endpoints
  static const String chatEndpoint = '/Chat/send';

  // Full URLs
  static const String registerUrl = '$baseUrl$registerEndpoint';
  static const String loginUrl = '$baseUrl$loginEndpoint';
  static const String chatUrl = '$baseUrl$chatEndpoint';

  // Timeout Configuration (tối ưu cho platform-specific)
  static Duration get connectTimeout {
    // Android emulator cần timeout dài hơn do NAT/proxy overhead
    // Web browser cũng cần timeout dài hơn
    if (PlatformUtils.isAndroid || PlatformUtils.isWeb) {
      return const Duration(seconds: 30); // 30s cho Android & Web
    }
    return const Duration(seconds: 15); // 15s cho các platform khác
  }

  static Duration get receiveTimeout {
    if (PlatformUtils.isAndroid || PlatformUtils.isWeb) {
      return const Duration(seconds: 45); // 45s cho Android & Web
    }
    return const Duration(seconds: 20); // 20s cho các platform khác
  }

  static Duration get sendTimeout {
    if (PlatformUtils.isAndroid || PlatformUtils.isWeb) {
      return const Duration(seconds: 25); // 25s cho Android & Web
    }
    return const Duration(seconds: 15); // 15s cho các platform khác
  }

  // Headers (tối ưu cho performance)
  static Map<String, String> get headers {
    final baseHeaders = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Cache-Control': 'no-cache',
      'User-Agent': 'Flutter-AI-Assistant/1.0 (${PlatformUtils.platformName})',
    };

    // Mobile platforms cần keep-alive để tái sử dụng connection
    if (PlatformUtils.isMobile) {
      baseHeaders['Connection'] = 'keep-alive';
      baseHeaders['Keep-Alive'] = 'timeout=30, max=100';
    }

    return baseHeaders;
  }

  // Storage Keys
  static const String tokenKey = 'auth_token';
  static const String usernameKey = 'username';
  static const String roleKey = 'user_role';
  static const String expiresAtKey = 'expires_at';

  // Retry Configuration - Tăng cho mobile & web
  static int get maxRetries {
    if (PlatformUtils.isAndroid || PlatformUtils.isWeb) {
      return 3; // 3 lần retry cho Android & Web
    }
    return 2; // 2 lần cho các platform khác
  }

  static Duration get retryDelay {
    if (PlatformUtils.isAndroid || PlatformUtils.isWeb) {
      return const Duration(seconds: 2); // 2s delay cho Android & Web
    }
    return const Duration(seconds: 1); // 1s cho các platform khác
  }
}
