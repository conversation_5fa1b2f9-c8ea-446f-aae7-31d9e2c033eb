import 'package:flutter/material.dart';
import 'payment_screen.dart';
import '../models/pricing_ui_models.dart';
import '../services/payment_service.dart';

class PricingScreen extends StatefulWidget {
  const PricingScreen({super.key});

  @override
  State<PricingScreen> createState() => _PricingScreenState();
}

class _PricingScreenState extends State<PricingScreen> {
  int _selectedPlanIndex = 1; // Default to Premium plan
  List<PricingPlan> _plans = [];
  bool _isLoading = true;
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _loadPlans();
  }

  void _loadPlans() async {
    try {
      // Try to load plans from API
      final response = await PaymentService.getAvailablePlans();

      if (response.success && response.data != null) {
        // Get current subscription to mark current plan
        final subscriptionResponse = await PaymentService.getSubscriptionStatus();
        String currentPlanType = 'Free';

        if (subscriptionResponse.success && subscriptionResponse.data != null) {
          currentPlanType = subscriptionResponse.data!.planType;
        }

        setState(() {
          _plans = response.data!.map((planInfo) {
            return PricingPlan.fromPlanInfo(
              planInfo,
              isCurrentPlan: planInfo.planType == currentPlanType,
            );
          }).toList();
          _isLoading = false;
        });
      } else {
        // Fallback to default plans
        _loadDefaultPlans();
      }
    } catch (e) {
      // Fallback to default plans
      _loadDefaultPlans();
    }
  }

  void _loadDefaultPlans() {
    setState(() {
      _plans = PricingPlan.getDefaultPlans();
      _isLoading = false;
    });
  }

  void _handleContinue() async {
    if (_selectedPlanIndex < 0 || _selectedPlanIndex >= _plans.length) return;

    final selectedPlan = _plans[_selectedPlanIndex];

    if (selectedPlan.isFree) {
      // Handle free plan selection
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Bạn đang sử dụng gói miễn phí'),
        ),
      );
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      // Navigate to payment screen
      await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PaymentScreen(selectedPlan: selectedPlan),
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Chọn gói Premium'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 0,
        centerTitle: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Simple header
                _buildSimpleHeader(),

                // Plans list
                Expanded(
                  child: _buildPlansList(),
                ),

                // Bottom action
                _buildBottomAction(),
              ],
            ),
    );
  }

  Widget _buildSimpleHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.rocket_launch,
            size: 48,
            color: Colors.blue[600],
          ),
          const SizedBox(height: 12),
          Text(
            'Nâng cấp trải nghiệm',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Chọn gói phù hợp với nhu cầu của bạn',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPlansList() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _plans.length,
      itemBuilder: (context, index) {
        final plan = _plans[index];
        final isSelected = index == _selectedPlanIndex;

        return _buildPlanCard(plan, isSelected, () {
          setState(() {
            _selectedPlanIndex = index;
          });
        });
      },
    );
  }

  Widget _buildPlanCard(PricingPlan plan, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.only(bottom: 16),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? Colors.blue : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header row
            Row(
              children: [
                // Plan icon
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: plan.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getPlanIcon(plan.planType),
                    color: plan.color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),

                // Plan name and popular badge
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            plan.name,
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          if (plan.isPopular) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.orange,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: const Text(
                                'Phổ biến',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      if (plan.isCurrentPlan)
                        Text(
                          'Gói hiện tại',
                          style: TextStyle(
                            color: Colors.green[600],
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                    ],
                  ),
                ),

                // Selection indicator
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: isSelected ? Colors.blue : Colors.grey[400]!,
                      width: 2,
                    ),
                    color: isSelected ? Colors.blue : Colors.transparent,
                  ),
                  child: isSelected
                      ? const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 16,
                        )
                      : null,
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Price
            Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  plan.price == '0' ? 'Miễn phí' : '${plan.price} VNĐ',
                  style: TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: plan.color,
                  ),
                ),
                if (plan.price != '0') ...[
                  const SizedBox(width: 4),
                  Text(
                    '/${plan.period}',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ],
            ),

            const SizedBox(height: 16),

            // Features
            ...plan.features.take(3).map((feature) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    color: Colors.green[600],
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      feature,
                      style: const TextStyle(fontSize: 14),
                    ),
                  ),
                ],
              ),
            )),

            // Show more features if available
            if (plan.features.length > 3)
              Text(
                '+${plan.features.length - 3} tính năng khác',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 12,
                  fontStyle: FontStyle.italic,
                ),
              ),
          ],
        ),
      ),
    );
  }

  IconData _getPlanIcon(String planType) {
    switch (planType.toLowerCase()) {
      case 'free':
        return Icons.free_breakfast;
      case 'basic':
        return Icons.star_border;
      case 'premium':
        return Icons.star;
      default:
        return Icons.help_outline;
    }
  }

  Widget _buildBottomAction() {
    if (_selectedPlanIndex < 0 || _selectedPlanIndex >= _plans.length) {
      return const SizedBox.shrink();
    }

    final selectedPlan = _plans[_selectedPlanIndex];

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 10,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Selected plan info
            Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: selectedPlan.color,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Gói đã chọn: ${selectedPlan.name}',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: selectedPlan.color,
                        ),
                      ),
                      Text(
                        selectedPlan.price == '0'
                            ? 'Miễn phí mãi mãi'
                            : '${selectedPlan.price} VNĐ/${selectedPlan.period}',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  selectedPlan.tokenLimitFormatted,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: selectedPlan.color,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Continue button
            SizedBox(
              width: double.infinity,
              height: 50,
              child: ElevatedButton(
                onPressed: _isProcessing ? null : _handleContinue,
                style: ElevatedButton.styleFrom(
                  backgroundColor: selectedPlan.isFree ? Colors.grey[400] : Colors.blue,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  elevation: 0,
                ),
                child: _isProcessing
                    ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : Text(
                        selectedPlan.isFree ? 'Gói hiện tại' : 'Tiếp tục',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
