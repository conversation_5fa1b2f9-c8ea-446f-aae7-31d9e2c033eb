import 'package:flutter/material.dart';
import 'payment_screen.dart';
import '../models/pricing_ui_models.dart';
import '../services/payment_service.dart';
import 'pricing/pricing_header.dart';
import 'pricing/pricing_card.dart';
import 'pricing/pricing_bottom_action.dart';

class PricingScreen extends StatefulWidget {
  const PricingScreen({super.key});

  @override
  State<PricingScreen> createState() => _PricingScreenState();
}

class _PricingScreenState extends State<PricingScreen> {
  int _selectedPlanIndex = 1; // Default to Premium plan
  List<PricingPlan> _plans = [];
  bool _isLoading = true;
  final bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _loadPlans();
  }

  void _loadPlans() async {
    try {
      // Try to load plans from API
      final response = await PaymentService.getAvailablePlans();

      if (response.success && response.data != null) {
        // Get current subscription to mark current plan
        final subscriptionResponse = await PaymentService.getSubscriptionStatus();
        String currentPlanType = 'Free';

        if (subscriptionResponse.success && subscriptionResponse.data != null) {
          currentPlanType = subscriptionResponse.data!.planType;
        }

        setState(() {
          _plans = response.data!.map((planInfo) {
            return PricingPlan.fromPlanInfo(
              planInfo,
              isCurrentPlan: planInfo.planType == currentPlanType,
            );
          }).toList();
          _isLoading = false;
        });
      } else {
        // Fallback to default plans
        _loadDefaultPlans();
      }
    } catch (e) {
      // Fallback to default plans
      _loadDefaultPlans();
    }
  }

  void _loadDefaultPlans() {
    setState(() {
      _plans = PricingPlan.getDefaultPlans();
      _isLoading = false;
    });
  }

  void _handleContinue() async {
    if (_selectedPlanIndex < 0 || _selectedPlanIndex >= _plans.length) return;

    final selectedPlan = _plans[_selectedPlanIndex];

    if (selectedPlan.isFree) {
      // Handle free plan selection
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Bạn đang sử dụng gói miễn phí'),
        ),
      );
      return;
    }

    // Navigate to payment screen
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PaymentScreen(selectedPlan: selectedPlan),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final isSmallScreen = screenHeight < 700;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Chọn gói Premium'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Header - flexible height based on screen size
                Flexible(
                  flex: isSmallScreen ? 2 : 3,
                  child: const PricingHeader(),
                ),

                // Plans List - takes remaining space
                Expanded(
                  flex: isSmallScreen ? 5 : 4,
                  child: ListView.builder(
                    padding: EdgeInsets.symmetric(
                      vertical: isSmallScreen ? 8 : 16,
                    ),
                    itemCount: _plans.length,
                    itemBuilder: (context, index) {
                      final plan = _plans[index];
                      return PricingCard(
                        plan: plan,
                        isSelected: index == _selectedPlanIndex,
                        onTap: () {
                          setState(() {
                            _selectedPlanIndex = index;
                          });
                        },
                      );
                    },
                  ),
                ),

                // Bottom Action - always visible
                if (_selectedPlanIndex >= 0 && _selectedPlanIndex < _plans.length)
                  PricingBottomAction(
                    selectedPlan: _plans[_selectedPlanIndex],
                    onContinue: _handleContinue,
                    isLoading: _isProcessing,
                  ),
              ],
            ),
    );
  }
}
