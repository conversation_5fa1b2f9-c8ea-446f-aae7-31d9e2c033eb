import 'package:flutter/material.dart';
import 'payment_screen.dart';
import '../models/pricing_ui_models.dart';
import '../services/payment_service.dart';
import 'pricing/simple_pricing_header.dart';
import 'pricing/pricing_plan_card.dart';
import 'pricing/pricing_bottom_section.dart';

class PricingScreen extends StatefulWidget {
  const PricingScreen({super.key});

  @override
  State<PricingScreen> createState() => _PricingScreenState();
}

class _PricingScreenState extends State<PricingScreen> {
  int _selectedPlanIndex = 1; // Default to Premium plan
  List<PricingPlan> _plans = [];
  bool _isLoading = true;
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _loadPlans();
  }

  void _loadPlans() async {
    print('[PricingScreen] Loading plans...');

    try {
      // Try to load plans from API
      final response = await PaymentService.getAvailablePlans();
      print('[PricingScreen] API response: success=${response.success}, data=${response.data?.length}');

      // Check if API returned valid data
      if (response.success && response.data != null && response.data!.isNotEmpty) {
        // Get current subscription to mark current plan
        final subscriptionResponse = await PaymentService.getSubscriptionStatus();
        String currentPlanType = 'Free';

        if (subscriptionResponse.success && subscriptionResponse.data != null) {
          currentPlanType = subscriptionResponse.data!.planType;
        }

        setState(() {
          _plans = response.data!.map((planInfo) {
            return PricingPlan.fromPlanInfo(
              planInfo,
              isCurrentPlan: planInfo.planType == currentPlanType,
            );
          }).toList();
          _isLoading = false;
          print('[PricingScreen] Plans loaded from API: ${_plans.length}');
        });
      } else {
        print('[PricingScreen] API returned empty data, loading default plans');
        // Fallback to default plans when API returns empty data
        _loadDefaultPlans();
      }
    } catch (e) {
      print('[PricingScreen] Exception: $e, loading default plans');
      // Fallback to default plans on error
      _loadDefaultPlans();
    }
  }

  void _loadDefaultPlans() {
    print('[PricingScreen] Loading default plans...');
    setState(() {
      _plans = PricingPlan.getDefaultPlans();
      _isLoading = false;
      print('[PricingScreen] Default plans loaded: ${_plans.length}');
    });
  }

  void _handleContinue() async {
    if (_selectedPlanIndex < 0 || _selectedPlanIndex >= _plans.length) return;

    final selectedPlan = _plans[_selectedPlanIndex];

    if (selectedPlan.isFree) {
      // Handle free plan selection
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Bạn đang sử dụng gói miễn phí'),
        ),
      );
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      // Navigate to payment screen
      await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => PaymentScreen(selectedPlan: selectedPlan),
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Chọn gói Premium'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black87,
        elevation: 0,
        centerTitle: true,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                // Header
                const SimplePricingHeader(),

                // Plans list
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _plans.length,
                    itemBuilder: (context, index) {
                      final plan = _plans[index];
                      return PricingPlanCard(
                        plan: plan,
                        isSelected: index == _selectedPlanIndex,
                        onTap: () {
                          setState(() {
                            _selectedPlanIndex = index;
                          });
                        },
                      );
                    },
                  ),
                ),

                // Bottom action
                if (_selectedPlanIndex >= 0 && _selectedPlanIndex < _plans.length)
                  PricingBottomSection(
                    selectedPlan: _plans[_selectedPlanIndex],
                    onContinue: _handleContinue,
                    isLoading: _isProcessing,
                  ),
              ],
            ),
    );
  }
}
